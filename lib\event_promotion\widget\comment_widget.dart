import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';

class CommentWidget extends StatelessWidget {
  const CommentWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Utility.imageLoader(
              url: '',
              placeholder: AppAssets.placeholderImage,
              height: 36,
              width: 36,
              shape: BoxShape.circle,
              // borderRadius: BorderRadius.circular(0),
              isShapeCircular: true,
              fit: BoxFit.cover,
            ),
            const Gap(8),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '<PERSON>',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  Text(
                    '2 mins ago',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.subText,
                        ),
                  )
                ],
              ),
            ),
          ],
        ),
        const Gap(8),
        Text(
          AppStrings.eventPromotionAutoExpire,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const Gap(8),
        Text(
          'Reply',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.primary,
              ),
        ),
        const Gap(20),
        const Divider(
          height: 0,
        )
      ],
    );
  }
}
