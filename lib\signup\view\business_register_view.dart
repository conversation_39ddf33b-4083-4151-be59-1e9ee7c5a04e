import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/photo_frame_widget.dart';

class BusinessRegisterView extends StatefulWidget {
  const BusinessRegisterView({super.key});

  @override
  State<BusinessRegisterView> createState() => _BusinessRegisterViewState();
}

class _BusinessRegisterViewState extends State<BusinessRegisterView> {
  final pickedImageFile = ValueNotifier<PickedFileModel?>(null);
  final nameOfOwnerController = TextEditingController();
  final nameOfCompanyController = TextEditingController();
  final mobileController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final obscureText = ValueNotifier<bool>(false);
  final termsAccepted = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Form(
        key: formKey,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.offGreen,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                AppStrings.bussinessSignupText,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(fontSize: 13, letterSpacing: 1.1, height: 1.5),
              ),
            ),
            const Gap(30),
            ValueListenableBuilder<PickedFileModel?>(
                valueListenable: pickedImageFile,
                builder: (context, file, _) {
                  return PhotoFrameWidget(
                    onTap: () async {
                      final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                      if (pickedImage != null) {
                        pickedImageFile.value = PickedFileModel(file: File(pickedImage.path));
                      }
                    },
                    onRemove: () {
                      pickedImageFile.value = null;
                    },
                    images: file,
                  );
                }),
            const Gap(30),
            AppTextFormField(
              title: 'Name of owner / handler',
              controller: nameOfOwnerController,
              textInputAction: TextInputAction.next,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter name of owner/handler';
                }
                return null;
              },
            ),
            const Gap(14),
            AppTextFormField(
              title: 'Name of company/ organisation/ firm/ - (optional)',
              controller: nameOfCompanyController,
              textInputAction: TextInputAction.next,
            ),
            const Gap(14),
            AppTextFormField(
              title: 'Mobile',
              controller: mobileController,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.phone,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter mobile number';
                }
                return null;
              },
            ),
            AppTextFormField(
              title: 'Email',
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter email';
                } else if (!Utility.isValidEmail(value)) {
                  return 'Please enter valid email';
                }
                return null;
              },
            ),
            const Gap(14),
            ValueListenableBuilder<bool>(
                valueListenable: obscureText,
                builder: (context, obscure, _) {
                  return AppTextFormField(
                    controller: passwordController,
                    title: 'Password',
                    hintText: 'Password',
                    obscureText: !obscure,
                    suffixIcon: IconButton(
                      onPressed: () {
                        obscureText.value = !obscure;
                      },
                      icon: AppSvgImage(
                        !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter password';
                      }
                      return null;
                    },
                  );
                }),
            const Gap(30),
            ValueListenableBuilder<bool>(
                valueListenable: termsAccepted,
                builder: (context, terms, _) {
                  return InkWell(
                    onTap: () {
                      termsAccepted.value = !termsAccepted.value;
                    },
                    child: Row(
                      children: [
                        AppSvgImage(terms ? AppAssets.selectedCheckboxIcon : AppAssets.unselectedCheckboxIcon),
                        const Gap(8),
                        Text(
                          'Terms and Conditions',
                          style: Theme.of(context).textTheme.bodyLarge,
                        )
                      ],
                    ),
                  );
                }),
            const Gap(30),
            CommonButton(
              onTap: () {
                validateForm();
              },
              text: 'Signup',
            ),
          ],
        ),
      ),
    );
  }

  void validateForm() {
    if (pickedImageFile.value == null) {
      Utility.toast(message: 'Please select profile image');
    } else if (formKey.currentState!.validate()) {
      if (termsAccepted.value) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const SetUpBusinessPage(),
          ),
        );
      } else {
        Utility.toast(message: 'Please accept terms and conditions');
      }
    }
  }
}
