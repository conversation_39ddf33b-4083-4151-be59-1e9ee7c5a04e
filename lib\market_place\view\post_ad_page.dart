import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/market_place/view/ad_duration_view.dart';
import 'package:leiuniverse/market_place/view/target_buyers_view.dart';
import 'package:leiuniverse/market_place/view/target_location_view.dart';
import 'package:leiuniverse/setup_business/view/business_image_view.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class PostAdPage extends StatefulWidget {
  const PostAdPage({super.key});

  @override
  State<PostAdPage> createState() => _PostAdPageState();
}

class _PostAdPageState extends State<PostAdPage> {
  final _formKey = GlobalKey<FormState>();
  final adTitleController = TextEditingController();
  final adDescriptionController = TextEditingController();
  final priceController = TextEditingController();
  final contactNumberController = TextEditingController();
  final postAdModel = ValueNotifier<PostOrEventModel>(PostOrEventModel());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Post Ad',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: ValueListenableBuilder(
              valueListenable: postAdModel,
              builder: (context, postAdValues, _) {
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Images of business or service - 5 max',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.primary,
                                ),
                          ),
                          const Gap(20),
                          BusinessImageView(
                            maxImage: 5,
                            onImageSelected: (images) {
                              postAdModel.value = postAdModel.value.copyWith(
                                images: images,
                              );
                            },
                          ),
                          const Gap(20),
                          AppTextFormField(
                            title: 'Ad title',
                            controller: adTitleController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a title';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              postAdModel.value = postAdModel.value.copyWith(
                                title: value,
                              );
                            },
                          ),
                          const Gap(14),
                          AppTextFormField(
                            title: 'Short description',
                            controller: adDescriptionController,
                            maxLines: 4,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a description';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              postAdModel.value = postAdModel.value.copyWith(
                                description: value,
                              );
                            },
                          ),
                          const Gap(14),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: AppTextFormField(
                                  title: 'Price',
                                  controller: priceController,
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter a price';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    postAdModel.value = postAdModel.value.copyWith(
                                      price: value,
                                    );
                                  },
                                ),
                              ),
                              const Gap(14),
                              Expanded(
                                child: AppTextFormField(
                                  title: 'Contact number',
                                  controller: contactNumberController,
                                  keyboardType: TextInputType.phone,
                                  titleColor: AppColors.subText,
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                        color: AppColors.subText,
                                      ),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter a contact number';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    postAdModel.value = postAdModel.value.copyWith(
                                      contactNumber: value,
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetBuyersView(
                      title: 'Target Your Buyers',
                      postOrEventModel: postAdValues,
                      onModelUpdated: (targetBuyers) {
                        postAdModel.value = postAdModel.value.copyWith(
                          category: targetBuyers.category,
                          subCategory: targetBuyers.subCategory,
                          childSubCategory: targetBuyers.childSubCategory,
                        );
                      },
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetLocationView(
                      postAdModel: postAdValues,
                      onModelUpdated: (targetLocation) {
                        postAdModel.value = postAdModel.value.copyWith(
                          country: targetLocation.country,
                          state: targetLocation.state,
                          city: targetLocation.city,
                        );
                      },
                    ),
                    const Divider(
                      height: 0,
                    ),
                    AdDurationView(
                      postAdModel: postAdValues,
                      onModelUpdated: (targetDuration) {
                        postAdModel.value = postAdModel.value.copyWith(
                          duration: targetDuration.duration,
                        );
                      },
                    ),
                    const Gap(10),
                    CommonButton(
                      text: 'Pay & Publish Ad',
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      onTap: () {
                        log(postAdValues.images?.length.toString() ?? '0');
                        // if (_formKey.currentState!.validate()) {}
                      },
                    ),
                    const Gap(30),
                  ],
                );
              }),
        ),
      ),
    );
  }
}
