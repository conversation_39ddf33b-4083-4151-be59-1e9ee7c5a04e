import 'dart:convert';
import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'local_storage_repository.dart';

abstract class ILocalStorageRepository {
  ILocalStorageRepository(this.preferences);
  final SharedPreferences preferences;

  Future<bool> setToken(String? value);

  String? get token;

  Future<UserModel> setUser(UserModel? user);

  UserModel? get getUser;

  Future<bool> clearAuth();
}
