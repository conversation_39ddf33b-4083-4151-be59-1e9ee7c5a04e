import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class UserDataWidget extends StatelessWidget {
  const UserDataWidget({super.key, this.name, this.businessName, this.imageUrl});
  final String? name;
  final String? businessName;
  final String? imageUrl;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Utility.imageLoader(
              url: '',
              placeholder: AppAssets.userAvtarImage,
              height: 60,
              width: 60,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 4),
                  blurRadius: 4,
                  spreadRadius: 0,
                  color: AppColors.black.withOpacity2(0.15),
                ),
              ],
            ),
            const Gap(14),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Text(
                        name ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                      const Gap(3),
                      const AppSvgImage(
                        AppAssets.verifyIcon,
                        height: 17,
                        width: 17,
                      ),
                    ],
                  ),
                  const Gap(4),
                  Text(
                    businessName ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: AppColors.subText,
                        ),
                  ),
                  const Gap(4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: AppColors.background,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const AppSvgImage(
                          AppAssets.locationIcon,
                          height: 16,
                          width: 16,
                        ),
                        const Gap(2),
                        Text(
                          'Vadodara, Gujarat, India',
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
