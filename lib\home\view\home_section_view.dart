import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/view/categories_page.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/event_promotion/view/event_promotion_page.dart';
import 'package:leiuniverse/home/<USER>/home_section_widget.dart';
import 'package:leiuniverse/inquiry/view/inquiry_page.dart';
import 'package:leiuniverse/market_place/view/market_place_page.dart';

class HomeSectionView extends StatefulWidget {
  const HomeSectionView({super.key});

  @override
  State<HomeSectionView> createState() => _HomeSectionViewState();
}

class _HomeSectionViewState extends State<HomeSectionView> {
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const Gap(20),
              Row(
                children: [
                  HomeSectionWidget(
                    title: 'Market Place',
                    icon: AppAssets.marketPlaceIcon,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MarketPlacePage(),
                        ),
                      );
                    },
                  ),
                  const Gap(20),
                  HomeSectionWidget(
                    title: 'Event Promotion',
                    icon: AppAssets.eventPromotionIcon,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const EventPromotionPage(),
                        ),
                      );
                    },
                  ),
                ],
              ),
              const Gap(20),
              Row(
                children: [
                  HomeSectionWidget(
                    title: 'Inquiry',
                    icon: AppAssets.inquiryIcon,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const InquiryPage(),
                        ),
                      );
                    },
                  ),
                  const Gap(20),
                  const HomeSectionWidget(title: 'Chat', icon: AppAssets.chatIcon),
                ],
              ),
              const Gap(20),
              Row(
                children: [
                  HomeSectionWidget(
                    title: 'Categories',
                    icon: AppAssets.categoriesIcon,
                    onTap: () {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => const CategoriesPage()));
                    },
                  ),
                  const Gap(20),
                  HomeSectionWidget(
                    title: 'My Favouites',
                    icon: AppAssets.favouitesIcon,
                    onTap: () {},
                  ),
                ],
              ),
              const Gap(30),
            ],
          ),
        ),
      ),
    );
  }
}
