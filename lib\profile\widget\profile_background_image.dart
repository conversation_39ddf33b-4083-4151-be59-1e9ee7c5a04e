import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/report/view/report_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ProfileBackgroundImage extends StatefulWidget {
  const ProfileBackgroundImage({super.key});

  @override
  State<ProfileBackgroundImage> createState() => _ProfileBackgroundImageState();
}

class _ProfileBackgroundImageState extends State<ProfileBackgroundImage> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 185,
      width: MediaQuery.sizeOf(context).width,
      child: Stack(
        children: [
          SizedBox(
            width: MediaQuery.sizeOf(context).width,
            child: Image.asset(
              AppAssets.userAvtarImage,
              fit: BoxFit.cover,
            ),
          ),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              color: AppColors.mirage.withOpacity2(0.44),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 40, 20, 0),
            child: Row(
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const AppSvgImage(AppAssets.roundBackIcon),
                ),
                const Spacer(),
                InkWell(
                  onTap: () {},
                  child: const AppSvgImage(AppAssets.roundShareIcon),
                ),
                const Gap(20),
                PopupMenuButton<int>(
                  tooltip: 'More options',
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  color: AppColors.white,
                  elevation: 6,
                  constraints: const BoxConstraints(minWidth: 151, minHeight: 62),
                  onSelected: (value) {
                    if (value == 1) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const ReportPage()),
                      );
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem<int>(
                      value: 1,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(top: 2.0),
                            child: AppSvgImage(AppAssets.reportIcon, height: 20, width: 20),
                          ),
                          const Gap(11),
                          Text(
                            "Report",
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                  child: const AppSvgImage(AppAssets.roundMenuIcon), // Your icon
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
