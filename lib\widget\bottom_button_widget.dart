import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/widget/common_button.dart';

class BottomButtonWidget extends StatelessWidget {
  const BottomButtonWidget({super.key, required this.onTap, required this.text});

  final VoidCallback onTap;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -3),
            blurRadius: 21,
            spreadRadius: 0,
            color: AppColors.navyColor.withOpacity2(0.06),
          ),
        ],
      ),
      child: CommonButton(
        onTap: onTap,
        margin: const EdgeInsets.only(bottom: 20),
        text: text,
      ),
    );
  }
}
