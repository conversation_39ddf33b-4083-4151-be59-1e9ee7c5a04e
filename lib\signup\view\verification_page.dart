import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/home/<USER>/home_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class VerificationPage extends StatefulWidget {
  const VerificationPage({super.key, required this.email});
  final String email;

  @override
  State<VerificationPage> createState() => _VerificationPageState();
}

class _VerificationPageState extends State<VerificationPage> {
  final otpController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Form(
              key: formKey,
              child: Column(
                children: [
                  const AppSvgImage(AppAssets.emailIcon),
                  const Gap(20),
                  Text(
                    'Verify Email',
                    style: Theme.of(context).textTheme.headlineLarge,
                  ),
                  const Gap(6),
                  Text(
                    'We have sent verification code on\n${widget.email}',
                    style: Theme.of(context).textTheme.titleSmall,
                    textAlign: TextAlign.center,
                  ),
                  const Gap(12),
                  Text(
                    "Please check your inbox for verification email.\nDidn't receive it? Check your spam folder or\nrequest a new one.",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(34, 30, 34, 20),
                    child: PinCodeTextField(
                      textInputAction: TextInputAction.go,
                      obscureText: false,
                      scrollPadding: EdgeInsets.zero,
                      backgroundColor: AppColors.bgColor,
                      pinTheme: PinTheme(
                        fieldWidth: 60,
                        fieldHeight: 66,
                        shape: PinCodeFieldShape.box,
                        borderRadius: BorderRadius.circular(12),
                        selectedFillColor: AppColors.background,
                        activeFillColor: AppColors.background,
                        activeColor: AppColors.background,
                        disabledColor: AppColors.background,
                        inactiveColor: AppColors.background,
                        selectedColor: AppColors.background,
                        inactiveFillColor: AppColors.background,
                      ),
                      textStyle: Theme.of(context).textTheme.headlineLarge,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      enableActiveFill: true,
                      appContext: context,
                      length: 4,
                      controller: otpController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter OTP';
                        }
                        return null;
                      },
                    ),
                  ),
                  InkWell(
                    onTap: () {},
                    child: Text(
                      'Resend Email',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 30),
                    child: CommonButton(
                      onTap: () {
                        if (formKey.currentState!.validate()) {
                          Navigator.pushAndRemoveUntil(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const HomePage(),
                            ),
                            (route) => false,
                          );
                        }
                      },
                      text: 'Verify',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
