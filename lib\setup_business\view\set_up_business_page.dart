import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page_2.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class SetUpBusinessPage extends StatefulWidget {
  const SetUpBusinessPage({super.key});

  @override
  State<SetUpBusinessPage> createState() => _SetUpBusinessPageState();
}

class _SetUpBusinessPageState extends State<SetUpBusinessPage> {
  final _formKey = GlobalKey<FormState>();
  final instagramController = TextEditingController();
  final facebookController = TextEditingController();
  final pincodeController = TextEditingController();

  final selectedCity = ValueNotifier<String?>(null);
  final selectedState = ValueNotifier<String?>(null);
  final selectedCountry = ValueNotifier<String?>(null);
  List<String> cities = ['City 1', 'City 2', 'City 3'];
  List<String> states = ['State 1', 'State 2', 'State 3'];
  List<String> countries = ['Country 1', 'Country 2', 'Country 3'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Setup Business',
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Social Media Links',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Instagram',
                      controller: instagramController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter Instagram link';
                        }
                        return null;
                      },
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Facebook',
                      controller: facebookController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter Facebook link';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              const Divider(
                height: 0,
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Address',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                    ),
                    const Gap(14),
                    Row(
                      children: [
                        Expanded(
                          child: AppTextFormField(
                            title: 'Pincode',
                            controller: pincodeController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter pincode';
                              }
                              return null;
                            },
                          ),
                        ),
                        const Gap(15),
                        Expanded(
                          child: ValueListenableBuilder<String?>(
                            valueListenable: selectedCity,
                            builder: (context, list, _) {
                              return AppDropDown(
                                title: 'City',
                                hintText: 'Select',
                                onSelect: (value) {
                                  selectedCity.value = value;
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select city';
                                  }
                                  return null;
                                },
                                items: cities.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                                selectedValue: list,
                              );
                            },
                          ),
                        )
                      ],
                    ),
                    const Gap(14),
                    Row(
                      children: [
                        Expanded(
                          child: ValueListenableBuilder<String?>(
                              valueListenable: selectedState,
                              builder: (context, list, _) {
                                return AppDropDown(
                                  title: 'State',
                                  hintText: 'Select',
                                  onSelect: (value) {
                                    selectedState.value = value;
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select state';
                                    }
                                    return null;
                                  },
                                  selectedValue: list,
                                  items: states.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                                );
                              }),
                        ),
                        const Gap(15),
                        Expanded(
                          child: ValueListenableBuilder<String?>(
                              valueListenable: selectedCountry,
                              builder: (context, list, _) {
                                return AppDropDown(
                                  title: 'Country',
                                  hintText: 'Select',
                                  onSelect: (value) {
                                    selectedCountry.value = value;
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select country';
                                    }
                                    return null;
                                  },
                                  selectedValue: list,
                                  items: countries.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                                );
                              }),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: CommonButton(
        onTap: () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => const SetUpBusinessPage2()));
        },
        margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
        text: 'Continue',
      ),
    );
  }
}
