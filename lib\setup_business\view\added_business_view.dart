import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/setup_business/view/add_business_service_page.dart';
import 'package:leiuniverse/setup_business/widget/business_info_widget.dart';
import 'package:leiuniverse/widget/common_button.dart';

class AddedBusinessView extends StatefulWidget {
  const AddedBusinessView({super.key});

  @override
  State<AddedBusinessView> createState() => _AddedBusinessViewState();
}

class _AddedBusinessViewState extends State<AddedBusinessView> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonButton(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddBusinessServicePage(),
                ),
              );
            },
            text: 'Add Business/Service',
          ),
          const Gap(20),
          ListView.separated(
            separatorBuilder: (context, index) => const Gap(16),
            itemCount: 5,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return BusinessInfoWidget(
                onEdit: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AddBusinessServicePage(),
                    ),
                  );
                },
                onDelete: () {},
              );
            },
          ),
        ],
      ),
    );
  }
}
