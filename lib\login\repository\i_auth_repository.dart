import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:leiuniverse/login/response/user_response.dart';

part 'auth_repository.dart';

abstract class IAuthRepository {
  IAuthRepository(this.client, this.localStorageRepository);
  final Client client;
  final ILocalStorageRepository localStorageRepository;

  ApiResult<UserResponse> login({
    required String email,
    required String password,
    required String firebaseId,
  });

  ApiResult<UserResponse> signUp({
    required String fullName,
    required String email,
    required String password,
    required String confirmPassword,
    required String firebaseId,
  });

  ApiResult<CommonResponse> verify({
    required String email,
    required String otp,
  });

  ApiResult<UserResponse> update({
    String? fullName,
    String? gender,
    String? universityId,
    String? year,
  });

  ApiResult<CommonResponse> resetPassword({
    required String email,
    required String password,
  });

  ApiResult<CommonResponse> forgotPassword({
    required String email,
  });

  ApiResult<CommonResponse> logout();
  ApiResult<CommonResponse> deleteAccount();

  ApiResult<UserResponse> userDetail({required String userId});
}
