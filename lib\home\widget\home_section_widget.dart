import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class HomeSectionWidget extends StatelessWidget {
  const HomeSectionWidget({super.key, required this.title, required this.icon, this.onTap});
  final String title;
  final String icon;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 157,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Text(title, style: Theme.of(context).textTheme.titleMedium),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.only(left: 8),
                decoration: BoxDecoration(boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFD521A).withOpacity2(0.045),
                    offset: const Offset(0, 8), // only bottom shadow
                    blurRadius: 20, // smooth falloff
                    spreadRadius: 0, // no harsh expansion
                  ),
                ]),
                child: AppSvgImage(icon),
              )
            ],
          ),
        ),
      ),
    );
  }
}
