import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/more/view/about_page.dart';
import 'package:leiuniverse/more/view/change_password_page.dart';
import 'package:leiuniverse/more/view/contact_us_page.dart';
import 'package:leiuniverse/more/view/faq_page.dart';
import 'package:leiuniverse/more/view/share_app_page.dart';
import 'package:leiuniverse/more/view/terms_and_privacy_page.dart';
import 'package:leiuniverse/more/widget/delete_profile_view.dart';
import 'package:leiuniverse/more/widget/more_element_view.dart';
import 'package:leiuniverse/more/widget/sign_out_view.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  State<MorePage> createState() => _MorePageState();
}

class _MorePageState extends State<MorePage> {
  final canSendMessage = ValueNotifier<bool>(false);
  final canCall = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'More',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColors.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Gap(6),
                    ValueListenableBuilder<bool>(
                        valueListenable: canSendMessage,
                        builder: (context, message, _) {
                          return MoreElementView(
                            icon: AppAssets.moreSendMessageIcon,
                            text: 'Any one can send message',
                            isSwitch: message,
                            onSwitch: (value) {
                              canSendMessage.value = value;
                            },
                          );
                        }),
                    ValueListenableBuilder<bool>(
                        valueListenable: canCall,
                        builder: (context, call, _) {
                          return MoreElementView(
                            icon: AppAssets.moreCallIcon,
                            text: 'Any one can call',
                            isSwitch: call,
                            onSwitch: (value) {
                              canCall.value = value;
                            },
                          );
                        }),
                    MoreElementView(
                      icon: AppAssets.changePasswordIcon,
                      text: 'Change password',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ChangePasswordPage(),
                          ),
                        );
                      },
                    ),
                    const Gap(6),
                  ],
                ),
              ),
              const Gap(20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColors.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Gap(6),
                    MoreElementView(
                      icon: AppAssets.termsPrivacyIcon,
                      text: 'Terms and privacy policy',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TermsAndPrivacyPage(),
                          ),
                        );
                      },
                    ),
                    MoreElementView(
                      icon: AppAssets.contactUsIcon,
                      text: 'Contact us',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ContactUsPage(),
                          ),
                        );
                      },
                    ),
                    MoreElementView(
                      icon: AppAssets.shareIcon,
                      text: 'Share app',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ShareAppPage(),
                          ),
                        );
                      },
                    ),
                    MoreElementView(
                      icon: AppAssets.faqIcon,
                      text: 'FAQs',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FaqPage(),
                          ),
                        );
                      },
                    ),
                    MoreElementView(
                      icon: AppAssets.aboutUsIcon,
                      text: 'About us',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AboutPage(),
                          ),
                        );
                      },
                    ),
                    MoreElementView(
                      icon: AppAssets.moreDeleteIcon,
                      text: 'Delete profile',
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: AppColors.white,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          builder: (context) {
                            return const DeleteProfileView();
                          },
                        );
                      },
                    ),
                    MoreElementView(
                      icon: AppAssets.signOutIcon,
                      text: 'Sign out',
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: AppColors.white,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          builder: (context) {
                            return const SignOutView();
                          },
                        );
                      },
                    ),
                    const Gap(6),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
