import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/onbording/view/onbording_3_page.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class Onbording2Page extends StatelessWidget {
  const Onbording2Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        hideDivider: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(AppAssets.onboardingImage2),
            const Gap(30),
            Text(
              'Create & Explore Profiles',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: AppColors.black),
            ),
            const Gap(10),
            Text(
              'Build your professional profile, share\nwork photos, and discover others by\ncategory, location, or talent.',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.black),
              textAlign: TextAlign.center,
            ),
            const Gap(76),
            CommonButton(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const Onbording3Page(),
                  ),
                );
              },
              text: 'Next',
              width: 165,
            ),
          ],
        ),
      ),
    );
  }
}
