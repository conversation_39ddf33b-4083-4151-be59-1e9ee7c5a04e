import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';

class TargetBuyersView extends StatefulWidget {
  const TargetBuyersView({super.key, required this.postOrEventModel, required this.onModelUpdated, this.title});
  final PostOrEventModel? postOrEventModel;
  final Function(PostOrEventModel) onModelUpdated;
  final String? title;

  @override
  State<TargetBuyersView> createState() => _TargetBuyersViewState();
}

class _TargetBuyersViewState extends State<TargetBuyersView> {
  final categoryList = ValueNotifier<List<String>>([
    'All',
    'Business',
    'Service',
  ]);
  final subCategoryList = ValueNotifier<List<String>>([
    'All',
    'Toys',
    'Clothing',
  ]);
  final childSubCategoryList = ValueNotifier<List<String>>([
    'All',
    'Pants',
    'Shirt',
  ]);

  final selectedCategory = ValueNotifier<String?>(null);
  final selectedSubCategory = ValueNotifier<String?>(null);
  final selectedChildSubCategory = ValueNotifier<String?>(null);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.title ?? '',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primary,
                ),
          ),
          const Gap(14),
          ValueListenableBuilder<List<String>>(
              valueListenable: categoryList,
              builder: (context, category, _) {
                return ValueListenableBuilder<String?>(
                    valueListenable: selectedCategory,
                    builder: (context, selectValue, _) {
                      return AppDropDown(
                        onSelect: (value) {
                          selectedCategory.value = value;
                          widget.onModelUpdated(widget.postOrEventModel!.copyWith(category: value));
                        },
                        items: category.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                        selectedValue: selectValue,
                        hintText: 'Select',
                        title: 'Category',
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a category';
                          }
                          return null;
                        },
                      );
                    });
              }),
          const Gap(14),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ValueListenableBuilder<List<String>>(
                    valueListenable: subCategoryList,
                    builder: (context, subCategory, _) {
                      return ValueListenableBuilder<String?>(
                          valueListenable: selectedSubCategory,
                          builder: (context, selectValue, _) {
                            return AppDropDown(
                              onSelect: (value) {
                                selectedSubCategory.value = value;
                                widget.onModelUpdated(widget.postOrEventModel!.copyWith(subCategory: value));
                              },
                              items: subCategory.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                              selectedValue: selectValue,
                              hintText: 'Select',
                              title: 'Sub category',
                              validator: (value) {
                                if (value == null) {
                                  return 'Please select a sub category';
                                }
                                return null;
                              },
                            );
                          });
                    }),
              ),
              const Gap(15),
              Expanded(
                child: ValueListenableBuilder<List<String>>(
                    valueListenable: childSubCategoryList,
                    builder: (context, childSubCategory, _) {
                      return ValueListenableBuilder<String?>(
                          valueListenable: selectedChildSubCategory,
                          builder: (context, selectValue, _) {
                            return AppDropDown(
                              onSelect: (value) {
                                selectedChildSubCategory.value = value;
                                widget.onModelUpdated(widget.postOrEventModel!.copyWith(childSubCategory: value));
                              },
                              items: childSubCategory.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                              selectedValue: selectValue,
                              hintText: 'Select',
                              title: 'Child sub category',
                              validator: (value) {
                                if (value == null) {
                                  return 'Please select a child sub category';
                                }
                                return null;
                              },
                            );
                          });
                    }),
              ),
            ],
          )
        ],
      ),
    );
  }
}
