name: leiuniverse
description: ""

publish_to: 'none' 


version: 1.0.0+1

environment:
  sdk: '>=3.3.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter


  cupertino_icons: ^1.0.6
  injectable: ^2.5.0
  get_it: ^8.0.3
  flutter_svg: ^2.2.0
  gap: ^3.0.1
  file_picker: ^10.2.0
  permission_handler: ^12.0.0+1
  fluttertoast: ^8.2.12
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  pin_code_fields: ^8.0.1
  flutter_html: ^3.0.0
  http: ^1.4.0
  equatable: ^2.0.7
  fpdart: ^1.1.1
  shared_preferences: ^2.5.3

dev_dependencies:
  injectable_generator: ^2.7.0
  build_runner: ^2.5.0
  json_serializable: ^6.9.5
  flutter_test:
    sdk: flutter


  flutter_lints: ^3.0.0


flutter:

  
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/svg/

  fonts:
    - family: SFProDisplay
      fonts:
        - asset: fonts/SFProDisplay-Regular.ttf
          weight: 400
        - asset: fonts/SFProDisplay-Medium.ttf
          weight: 500
        - asset: fonts/SFProDisplay-Semibold.ttf
          weight: 600
        - asset: fonts/SFProDisplay-Bold.ttf
          weight: 700
        - asset: fonts/SFProDisplay-Heavy.ttf
          weight: 900

