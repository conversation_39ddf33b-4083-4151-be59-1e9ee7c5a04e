import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/login/widget/login_option_widget.dart';
import 'package:leiuniverse/signup/view/sign_up_page.dart';
import 'package:leiuniverse/signup/view/verification_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final isBussinessLogin = ValueNotifier<bool>(false);
  final obscureText = ValueNotifier<bool>(false);
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        hideDivider: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppAssets.logo,
                        height: 50,
                        width: 50,
                      ),
                      const Gap(10),
                      Text(
                        'LEI-Universe',
                        style: Theme.of(context).textTheme.headlineLarge,
                      ),
                    ],
                  ),
                ),
                const Gap(22),
                Text(
                  'Login',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const Gap(24),
                ValueListenableBuilder<bool>(
                    valueListenable: isBussinessLogin,
                    builder: (context, value, _) {
                      return Row(
                        children: [
                          LoginOptionWidget(
                            isSelected: !value,
                            title: 'Visitor ID',
                            onTap: () {
                              isBussinessLogin.value = false;
                            },
                          ),
                          const Gap(40),
                          LoginOptionWidget(
                            isSelected: value,
                            title: 'Bussiness ID',
                            onTap: () {
                              isBussinessLogin.value = true;
                            },
                          ),
                        ],
                      );
                    }),
                const Gap(14),
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppColors.offGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ValueListenableBuilder<bool>(
                      valueListenable: isBussinessLogin,
                      builder: (context, value, _) {
                        return Text(
                          value ? AppStrings.bussinessLoginText : AppStrings.visitorLoginText,
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge
                              ?.copyWith(fontSize: 13, letterSpacing: 1.1, height: 1.5),
                        );
                      }),
                ),
                const Gap(14),
                AppTextFormField(
                  controller: emailController,
                  title: 'Email',
                  hintText: '<EMAIL>',
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter email';
                    } else if (!Utility.isValidEmail(value)) {
                      return 'Please enter valid email';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                ValueListenableBuilder<bool>(
                    valueListenable: obscureText,
                    builder: (context, obscure, _) {
                      return AppTextFormField(
                        controller: passwordController,
                        title: 'Password',
                        hintText: 'Password',
                        obscureText: !obscure,
                        suffixIcon: IconButton(
                          onPressed: () {
                            obscureText.value = !obscure;
                          },
                          icon: AppSvgImage(
                            !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter password';
                          }
                          return null;
                        },
                      );
                    }),
                const Gap(4),
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    'Forgot password?',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                const Gap(30),
                CommonButton(
                  onTap: () {
                    // if (formKey.currentState!.validate()) {}
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const VerificationPage(
                                  email: '<EMAIL>',
                                )));
                  },
                  text: 'Login',
                ),
                const Gap(22),
                Center(
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SignUpPage(),
                        ),
                      );
                    },
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(text: 'Don\'t have an account?', style: Theme.of(context).textTheme.bodyMedium),
                          TextSpan(
                            text: ' Signup ',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.primary),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Gap(22),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
