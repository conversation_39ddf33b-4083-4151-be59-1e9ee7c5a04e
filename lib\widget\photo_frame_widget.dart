import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class PhotoFrameWidget extends StatelessWidget {
  final VoidCallback? onTap;
  final PickedFileModel? images;
  final VoidCallback? onRemove;

  const PhotoFrameWidget({
    super.key,
    this.onTap,
    this.images,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: 140,
            height: 153,
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withOpacity2(0.1),
                  blurRadius: 11.54,
                  spreadRadius: 0,
                  offset: const Offset(0, 5.29),
                ),
              ],
            ),
            child: images == null
                ? Container(
                    margin: const EdgeInsets.all(11),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const AppSvgImage(AppAssets.cameraIcon),
                        const Gap(8),
                        Text(
                          'Add Photo',
                          style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                        ),
                      ],
                    ),
                  )
                : Container(
                    margin: const EdgeInsets.all(11),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(6),
                      image: DecorationImage(
                        image: FileImage(File(images!.file!.path)),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
          ),
        ),
        if (images != null && onRemove != null)
          Positioned(
            top: 2.25,
            right: 2.25,
            child: GestureDetector(
              onTap: onRemove,
              child: const AppSvgImage(AppAssets.cancelIcon, width: 22, height: 22),
            ),
          ),
      ],
    );
  }
}
