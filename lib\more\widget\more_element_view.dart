import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class MoreElementView extends StatelessWidget {
  const MoreElementView(
      {super.key, required this.icon, required this.text, this.isSwitch = false, this.onSwitch, this.onTap});
  final String icon;
  final String text;
  final bool isSwitch;
  final void Function(bool)? onSwitch;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        child: Row(
          children: [
            AppSvgImage(icon),
            const Gap(6),
            Expanded(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
            if (onSwitch != null)
              Transform.scale(
                scale: 0.7,
                child: SizedBox(
                  height: 22,
                  width: 38,
                  child: CupertinoSwitch(
                    value: isSwitch,
                    onChanged: onSwitch,
                    activeTrackColor: AppColors.primary,
                  ),
                ),
              )
            else
              const AppSvgImage(AppAssets.rightArrowIconIos),
          ],
        ),
      ),
    );
  }
}
