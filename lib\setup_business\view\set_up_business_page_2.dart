import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/payment_done/view/payment_done_page.dart';
import 'package:leiuniverse/setup_business/view/add_business_service_page.dart';
import 'package:leiuniverse/setup_business/view/added_business_view.dart';
import 'package:leiuniverse/setup_business/view/empty_business_view.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/common_button_without_shadow.dart';

class SetUpBusinessPage2 extends StatefulWidget {
  const SetUpBusinessPage2({super.key});

  @override
  State<SetUpBusinessPage2> createState() => _SetUpBusinessPage2State();
}

class _SetUpBusinessPage2State extends State<SetUpBusinessPage2> {
  final isBusinessAdded = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: isBusinessAdded,
        builder: (context, isBusinessAvailable, _) {
          return Scaffold(
            backgroundColor: isBusinessAvailable ? AppColors.background : AppColors.white,
            appBar: CommonAppBar(
              onBackTap: () {
                Navigator.pop(context);
              },
              title: 'Setup Business',
            ),
            body: isBusinessAvailable
                ? const SingleChildScrollView(
                    child: AddedBusinessView(),
                  )
                : EmptyBusinessView(
                    onTap: () async {
                      final result = await Navigator.push<bool>(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AddBusinessServicePage(),
                        ),
                      );
                      if (result != null && result) {
                        isBusinessAdded.value = result;
                      }
                    },
                  ),
            bottomNavigationBar: isBusinessAvailable
                ? CommonButton(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const PaymentDonePage(),
                        ),
                      );
                    },
                    margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                    text: 'Continue to pay Rs.250',
                  )
                : CommonButtonWithoutShadow(
                    onTap: () {},
                    margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                    text: 'Continue',
                  ),
          );
        });
  }
}
