import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/common_button.dart';

class SignOutView extends StatelessWidget {
  const SignOutView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Sign Out',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Gap(4),
          Text(
            'Are you sure you want to sign out?',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Gap(30),
          Row(
            children: [
              Expanded(
                child: CommonButton(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  text: 'No',
                ),
              ),
              const Gap(15),
              Expanded(
                child: CommonButton(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  text: 'Sign out',
                  removeShadow: true,
                  backgroundColor: AppColors.background,
                  textColor: AppColors.ebonyClay,
                  borderColor: AppColors.background,
                ),
              ),
            ],
          ),
          const Gap(20),
        ],
      ),
    );
  }
}
