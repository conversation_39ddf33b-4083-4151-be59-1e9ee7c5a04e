import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class BusinessInfoWidget extends StatelessWidget {
  const BusinessInfoWidget({super.key, this.onDelete, this.onEdit});
  final void Function()? onDelete;
  final void Function()? onEdit;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Musician',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Gap(10),
          const Wrap(
            spacing: 10,
            runSpacing: 10,
            children: [
              CategoryTypeWidget(title: 'Keyboard'),
              CategoryTypeWidget(title: 'Guitar'),
            ],
          ),
          const Gap(20),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                4,
                (index) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 14),
                    child: Utility.imageLoader(
                      url: '',
                      placeholder: AppAssets.placeholderImage,
                      borderRadius: BorderRadius.circular(8),
                      height: 93,
                      width: 93,
                      fit: BoxFit.cover,
                    ),
                  );
                },
              ),
            ),
          ),
          const Gap(14),
          const Divider(
            height: 0,
          ),
          const Gap(12),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Rs.150 per year',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                ),
              ),
              const Gap(4),
              InkWell(
                onTap: onEdit,
                child: const AppSvgImage(
                  AppAssets.editIcon,
                ),
              ),
              const Gap(16),
              InkWell(
                onTap: onDelete,
                child: const AppSvgImage(
                  AppAssets.deleteIcon,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
