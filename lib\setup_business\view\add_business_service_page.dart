import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/setup_business/view/business_image_view.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class AddBusinessServicePage extends StatefulWidget {
  const AddBusinessServicePage({
    super.key,
  });

  @override
  State<AddBusinessServicePage> createState() => _AddBusinessServicePageState();
}

class _AddBusinessServicePageState extends State<AddBusinessServicePage> {
  final _formKey = GlobalKey<FormState>();
  final shortDiscriptionController = TextEditingController();
  final selectedTypeOfBusinessOrService = ValueNotifier<String?>(null);
  final selectedSubCategory = ValueNotifier<String?>(null);
  final selectedChildSubCategory = ValueNotifier<String?>(null);
  List<String> typeOfBusinessOrServiceList = [
    'Type of business or service 1',
    'Type of business or service 2',
    'Type of business or service 3'
  ];

  List<String> subCategoriesList = ['Sub Category 1', 'Sub Category 2', 'Sub Category 3'];
  List<String> childSubCategoriesList = ['Child Sub Category 1', 'Child Sub Category 2', 'Child Sub Category 3'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Add Business/Service',
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    ValueListenableBuilder<String?>(
                      valueListenable: selectedTypeOfBusinessOrService,
                      builder: (context, typeOfBusinessOrService, _) {
                        return AppDropDown(
                          selectedValue: typeOfBusinessOrService,
                          onSelect: (value) {
                            selectedTypeOfBusinessOrService.value = value;
                          },
                          items: typeOfBusinessOrServiceList
                              .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                              .toList(),
                          hintText: 'Select',
                          title: 'Type of business or service',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select type of business or service';
                            }
                            return null;
                          },
                        );
                      },
                    ),
                    const Gap(14),
                    ValueListenableBuilder<String?>(
                        valueListenable: selectedSubCategory,
                        builder: (context, subCategory, _) {
                          return AppDropDown(
                            selectedValue: subCategory,
                            onSelect: (valueOfCategory) {
                              selectedSubCategory.value = valueOfCategory;
                            },
                            items: subCategoriesList.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                            hintText: 'Select',
                            title: 'Sub category',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select sub category';
                              }
                              return null;
                            },
                          );
                        }),
                    const Gap(14),
                    ValueListenableBuilder<String?>(
                      valueListenable: selectedSubCategory,
                      builder: (context, childSubCategory, _) {
                        return AppDropDown(
                          selectedValue: childSubCategory,
                          onSelect: (valueOfCategory) {
                            selectedChildSubCategory.value = valueOfCategory;
                          },
                          items: childSubCategoriesList.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                          hintText: 'Select',
                          title: 'Child sub category',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select child sub category';
                            }
                            return null;
                          },
                        );
                      },
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Short discription',
                      controller: shortDiscriptionController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter short discription';
                        }
                        return null;
                      },
                      maxLines: 5,
                    ),
                  ],
                ),
              ),
              const Divider(
                height: 0,
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Images of business or service',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.primary, fontSize: 16),
                    ),
                    const Gap(20),
                    // Business Image View
                    BusinessImageView(
                      onImageSelected: (images) {},
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: CommonButton(
        onTap: () {
          Navigator.pop(context, true);
        },
        margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
        text: 'Continue',
      ),
    );
  }
}
