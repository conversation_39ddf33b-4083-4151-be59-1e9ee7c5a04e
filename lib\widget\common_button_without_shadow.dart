import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class CommonButtonWithoutShadow extends StatelessWidget {
  const CommonButtonWithoutShadow({
    this.text,
    required this.onTap,
    super.key,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.padding,
    this.margin,
    this.textStyle,
    this.height,
  });
  final String? text;
  final void Function()? onTap;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: width ?? double.infinity,
        maxHeight: height ?? double.infinity,
      ),
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.background,
          padding: padding ?? EdgeInsets.symmetric(vertical: isLoading ? 10 : 17),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
        onPressed: isLoading ? () {} : onTap,
        child: isLoading
            ? const CustomProgressIndecator(
                size: 34,
                color: AppColors.primary,
              )
            : Text(
                text ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: textStyle ??
                    Theme.of(context)
                        .elevatedButtonTheme
                        .style
                        ?.textStyle
                        ?.resolve({})?.copyWith(color: AppColors.text),
              ),
      ),
    );
  }
}
