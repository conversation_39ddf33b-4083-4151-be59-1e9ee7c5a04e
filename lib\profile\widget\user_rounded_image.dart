import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';

class UserRoundedImage extends StatelessWidget {
  const UserRoundedImage({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Container(
        margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 67),
        height: 100,
        width: 100,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
              color: AppColors.black.withOpacity2(0.15),
            ),
          ],
        ),
        child: Image.asset(AppAssets.userAvtarImage, fit: BoxFit.fill),
      ),
    );
  }
}
