import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class MarketPlaceDetailsPage extends StatefulWidget {
  const MarketPlaceDetailsPage({super.key});

  @override
  State<MarketPlaceDetailsPage> createState() => _MarketPlaceDetailsPageState();
}

class _MarketPlaceDetailsPageState extends State<MarketPlaceDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Ad Detail',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: const [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: AppSvgImage(AppAssets.dislikeIcon, height: 24, width: 24),
          ),
          Gap(20),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: AppSvgImage(AppAssets.shareIcon, height: 24, width: 24),
          ),
          Gap(20),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: AppSvgImage(AppAssets.menuDotsIcon, height: 24, width: 24),
          ),
          Gap(24),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Utility.imageLoader(
                        url: '',
                        placeholder: AppAssets.userAvtarImage,
                        height: 44,
                        width: 44,
                        fit: BoxFit.cover,
                        borderRadius: BorderRadius.circular(6),
                        boxShadow: [
                          BoxShadow(
                            offset: const Offset(0, 4),
                            blurRadius: 4,
                            spreadRadius: 0,
                            color: AppColors.black.withOpacity2(0.15),
                          ),
                        ],
                      ),
                      const Gap(14),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  child: Text(
                                    'John Doe',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                                ),
                                const Gap(3),
                                const AppSvgImage(AppAssets.verifyIcon),
                              ],
                            ),
                            Text(
                              'Business name here',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                            ),
                          ],
                        ),
                      ),
                      const Gap(12),
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: AppSvgImage(AppAssets.rightArrowIconIos, height: 24, width: 24),
                      ),
                    ],
                  ),
                  const Gap(12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 60.0),
                    child: Row(
                      children: [
                        const AppSvgImage(
                          AppAssets.dislikeHandIcon,
                        ),
                        const Gap(6),
                        Text(
                          '100 Likes',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const Gap(20),
            Text(
              'Business/Services Ad',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.subText,
                  ),
            ),
            const Gap(20),
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Add title here',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  const Gap(10),
                  const Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: [
                      CategoryTypeWidget(title: 'Keyboard', color: AppColors.background),
                      CategoryTypeWidget(title: 'Guitar', color: AppColors.background),
                    ],
                  ),
                  const Gap(10),
                  Text(
                    '\$150',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const Gap(14),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: List.generate(
                        4,
                        (index) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 14),
                            child: Utility.imageLoader(
                              url: '',
                              placeholder: AppAssets.placeholderImage,
                              borderRadius: BorderRadius.circular(8),
                              height: 93,
                              width: 93,
                              fit: BoxFit.cover,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const Gap(14),
                  const Divider(
                    height: 0,
                  ),
                  const Gap(14),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: CommonButton(
                          onTap: () {},
                          text: 'Message',
                          removeShadow: true,
                          height: 42,
                          padding: EdgeInsets.zero,
                          borderRadius: 200,
                          fontSize: 16,
                          icon: const AppSvgImage(AppAssets.messageIcon),
                        ),
                      ),
                      const Gap(15),
                      Expanded(
                        child: CommonButton(
                          onTap: () {},
                          text: 'Call',
                          borderRadius: 200,
                          fontSize: 16,
                          height: 42,
                          isLessShadow: true,
                          padding: EdgeInsets.zero,
                          icon: const AppSvgImage(AppAssets.callIcon),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
