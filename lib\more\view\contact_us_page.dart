import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class ContactUsPage extends StatefulWidget {
  const ContactUsPage({super.key});

  @override
  State<ContactUsPage> createState() => _ContactUsPageState();
}

class _ContactUsPageState extends State<ContactUsPage> {
  final subjectController = TextEditingController();
  final messageController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Contact us',
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              margin: const EdgeInsets.all(20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: AppColors.primary,
              ),
              child: Column(
                children: [
                  Text(
                    'Get in Touch',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: AppColors.white),
                  ),
                  Text(
                    'Say something to start',
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.white),
                  ),
                  const Gap(12),
                  contactView(
                    icon: AppAssets.contactUsCallIcon,
                    title: '+91 1234567890',
                    onTap: () {},
                  ),
                  contactView(
                    icon: AppAssets.contactEmailIcon,
                    title: '<EMAIL>',
                    onTap: () {},
                  ),
                  contactView(
                    icon: AppAssets.mapLocationIcon,
                    title: 'www.leiuniverse.com',
                    onTap: () {},
                  ),
                  contactView(
                    icon: AppAssets.contactUsLocationIcon,
                    title: '132 Dartmouth Street Boston, Massachusetts 02156 United States',
                    onTap: () {},
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {},
                        child: const Padding(
                          padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                          child: AppSvgImage(AppAssets.contactUsFacebookIcon),
                        ),
                      ),
                      InkWell(
                        onTap: () {},
                        child: const Padding(
                          padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                          child: AppSvgImage(AppAssets.contactUsTwiterIcon),
                        ),
                      ),
                      InkWell(
                        onTap: () {},
                        child: const Padding(
                          padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                          child: AppSvgImage(AppAssets.contactUsInstagramIcon),
                        ),
                      ),
                      InkWell(
                        onTap: () {},
                        child: const Padding(
                          padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                          child: AppSvgImage(AppAssets.contactUsYoutubeIcon),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(20),
              color: AppColors.white,
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Drop a Message', style: Theme.of(context).textTheme.headlineMedium),
                    const Gap(20),
                    AppTextFormField(
                      controller: subjectController,
                      title: 'Subject*',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter subject';
                        }
                        return null;
                      },
                    ),
                    const Gap(20),
                    AppTextFormField(
                      controller: messageController,
                      maxLines: 3,
                      title: 'Mesasage*',
                      hintText: 'Enter details here',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter message';
                        }
                        return null;
                      },
                    ),
                    const Gap(30),
                    CommonButton(
                      onTap: () {
                        if (formKey.currentState!.validate()) {}
                      },
                      text: 'Send Message',
                    ),
                    const Gap(10),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget contactView({required String icon, required String title, void Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 50),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppSvgImage(icon),
            const Gap(7),
            Text(
              title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.white),
            )
          ],
        ),
      ),
    );
  }
}
