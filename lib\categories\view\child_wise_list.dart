import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/widget/profile_view_widget.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class CategoriesWiseList extends StatefulWidget {
  const CategoriesWiseList({super.key});

  @override
  State<CategoriesWiseList> createState() => _CategoriesWiseListState();
}

class _CategoriesWiseListState extends State<CategoriesWiseList> {
  final searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: ('Child Sub Category Name'),
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextFormField(
              controller: searchController,
              hintText: 'Search...',
              fillColor: AppColors.white,
              prefixIcon: const Padding(
                padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                child: AppSvgImage(
                  AppAssets.searchIcon,
                  color: AppColors.subText,
                ),
              ),
            ),
            Flexible(
              child: ListView.separated(
                itemCount: 10,
                separatorBuilder: (context, index) {
                  return const Gap(20);
                },
                padding: const EdgeInsets.symmetric(vertical: 20),
                itemBuilder: (context, index) {
                  return UserProfileWidget(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const ProfileDetailPage()),
                      );
                    },
                    likeOnTap: () {},
                    callOnTap: () {},
                    messageOnTap: () {},
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
