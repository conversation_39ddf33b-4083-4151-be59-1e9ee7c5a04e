import 'package:flutter/material.dart';
import 'package:leiuniverse/categories/view/child_wise_list.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class CategoriesPage extends StatelessWidget {
  const CategoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, String>> categories = [
      {
        'title': 'Musicians',
        'image': 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Bands',
        'image': 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Singer',
        'image': 'https://images.unsplash.com/photo-1521335629791-ce4aec67dd53?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Decoration',
        'image': 'https://images.unsplash.com/photo-1550639525-c97d455acf70?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Film Production Industry',
        'image': 'https://images.unsplash.com/photo-1542206395-9feb3edaa68c?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Anchor',
        'image': 'https://images.unsplash.com/photo-1607746882042-944635dfe10e?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Makeup Artist',
        'image': 'https://images.unsplash.com/photo-1522337660859-02fbefca4702?auto=format&fit=crop&w=500&q=80'
      },
      {
        'title': 'Seller /Retailer',
        'image': 'https://images.unsplash.com/photo-1542831371-d531d36971e6?auto=format&fit=crop&w=500&q=80'
      },
    ];
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Categories',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
          padding: const EdgeInsets.all(12.0),
          child: GridView.builder(
            itemCount: categories.length,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
            ),
            itemBuilder: (context, index) {
              final category = categories[index];
              return categoriesWidget(
                category['title']!,
                category['image']!,
                context,
              );
            },
          )),
    );
  }

  Widget categoriesWidget(String title, String imageUrl, BuildContext context) {
    return InkWell(
      onTap: () {
        // Handle category tap
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const CategoriesWiseList(),
          ),
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          // fit: StackFit.expand,
          children: [
            SizedBox(
              height: 200,
              width: 200,
              child: Image.network(
                imageUrl,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(child: CircularProgressIndicator());
                },
                errorBuilder: (context, error, stackTrace) => Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.broken_image, size: 40),
                ),
              ),
            ),
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.black54, Colors.transparent],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                ),
              ),
            ),
            Positioned(
              bottom: 10,
              left: 10,
              right: 10,
              child: Text(
                title,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
