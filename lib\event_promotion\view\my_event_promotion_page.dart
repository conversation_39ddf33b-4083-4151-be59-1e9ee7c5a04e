import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/event_promotion/widget/event_promotion_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class MyEventPromotionPage extends StatefulWidget {
  const MyEventPromotionPage({super.key});

  @override
  State<MyEventPromotionPage> createState() => _MyEventPromotionPageState();
}

class _MyEventPromotionPageState extends State<MyEventPromotionPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'My Event/Promotion',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ListView.separated(
        itemCount: 10,
        padding: const EdgeInsets.all(20),
        itemBuilder: (context, index) {
          return EventPromotionWidget(
            onTap: () {},
            onEditTap: () {},
            onDeleteTap: () {},
            isFromMyEvent: true,
          );
        },
        separatorBuilder: (context, index) {
          return const Gap(14);
        },
      ),
    );
  }
}
