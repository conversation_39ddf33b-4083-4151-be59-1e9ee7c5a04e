import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/widget/user_data_widget.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/icon_text_widget.dart';

class UserProfileWidget extends StatelessWidget {
  const UserProfileWidget({super.key, this.onTap, this.likeOnTap, this.callOnTap, this.messageOnTap});
  final void Function()? onTap;
  final void Function()? likeOnTap;
  final void Function()? callOnTap;
  final void Function()? messageOnTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const UserDataWidget(
              name: 'John Doe',
              businessName: 'Business name here',
              imageUrl: AppAssets.userAvtarImage,
            ),
            const Gap(14),
            Wrap(
              runSpacing: 10,
              spacing: 10,
              children: List.generate(
                3,
                (index) {
                  return const CategoryTypeWidget(
                    title: 'Keyboard',
                    color: AppColors.offGreen,
                  );
                },
              ),
            ),
            const Gap(14),
            const Divider(
              height: 0,
            ),
            const Gap(14),
            Row(
              children: [
                IconTextWidget(
                  icon: AppAssets.likeHandIcon,
                  text: '550 Likes',
                  onTap: likeOnTap,
                ),
                const Spacer(),
                InkWell(
                  onTap: messageOnTap,
                  child: const AppSvgImage(
                    AppAssets.roundMessageIcon,
                  ),
                ),
                const Gap(20),
                CommonButton(
                  onTap: () {
                    callOnTap?.call();
                  },
                  isLessShadow: true,
                  width: 93,
                  height: 34,
                  padding: EdgeInsets.zero,
                  borderRadius: 200,
                  text: 'Call',
                  icon: const AppSvgImage(AppAssets.callIcon, height: 20, width: 20),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
