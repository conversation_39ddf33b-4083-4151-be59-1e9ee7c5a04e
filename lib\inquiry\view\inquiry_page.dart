import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/market_place/view/target_buyers_view.dart';
import 'package:leiuniverse/market_place/view/target_location_view.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class InquiryPage extends StatefulWidget {
  const InquiryPage({super.key});

  @override
  State<InquiryPage> createState() => _InquiryPageState();
}

class _InquiryPageState extends State<InquiryPage> {
  final _formKey = GlobalKey<FormState>();
  final adTitleController = TextEditingController();
  final adDescriptionController = TextEditingController();
  final priceController = TextEditingController();
  final contactNumberController = TextEditingController();
  final inquiryData = ValueNotifier<PostOrEventModel>(PostOrEventModel());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Inquiry',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: ValueListenableBuilder(
              valueListenable: inquiryData,
              builder: (context, postAdValues, _) {
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: AppColors.offGreen,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Suitable user will contact you back once you send inquiry',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontSize: 13,
                                  ),
                            ),
                          ),
                          const Gap(20),
                          AppTextFormField(
                            title: 'Short description',
                            controller: adDescriptionController,
                            maxLines: 4,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a description';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              inquiryData.value = inquiryData.value.copyWith(
                                description: value,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetLocationView(
                      postAdModel: postAdValues,
                      onModelUpdated: (targetLocation) {
                        inquiryData.value = inquiryData.value.copyWith(
                          country: targetLocation.country,
                          state: targetLocation.state,
                          city: targetLocation.city,
                        );
                      },
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetBuyersView(
                      title: 'Target Business',
                      postOrEventModel: postAdValues,
                      onModelUpdated: (targetBuyers) {
                        inquiryData.value = inquiryData.value.copyWith(
                          category: targetBuyers.category,
                          subCategory: targetBuyers.subCategory,
                          childSubCategory: targetBuyers.childSubCategory,
                        );
                      },
                    ),
                    const Gap(10),
                    CommonButton(
                      text: 'Send Inquiry',
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      onTap: () {
                        log(postAdValues.images?.length.toString() ?? '0');
                        // if (_formKey.currentState!.validate()) {}
                      },
                    ),
                    const Gap(30),
                  ],
                );
              }),
        ),
      ),
    );
  }
}
