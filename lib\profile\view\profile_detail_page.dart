import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/profile/widget/profile_background_image.dart';
import 'package:leiuniverse/profile/widget/profile_user_detail.dart';

class ProfileDetailPage extends StatefulWidget {
  const ProfileDetailPage({super.key});

  @override
  State<ProfileDetailPage> createState() => _ProfileDetailPageState();
}

class _ProfileDetailPageState extends State<ProfileDetailPage> {
  @override
  Widget build(BuildContext context) {
    double statusBar = MediaQuery.of(context).padding.top;
    double avatarSize = 100;
    double avatarMarginTop = statusBar + 67;
    double contentMarginTop = avatarMarginTop + (avatarSize / 2);

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [],
        body: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  const ProfileBackgroundImage(),

                  // Profile content card
                  Container(
                    width: MediaQuery.sizeOf(context).width,
                    margin: EdgeInsets.only(top: contentMarginTop),
                    padding: const EdgeInsets.only(top: 57), // space under avatar
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(24),
                        topLeft: Radius.circular(24),
                      ),
                      color: AppColors.white,
                    ),
                    child: const ProfileUserDetail(),
                  ),

                  // Avatar Image Positioned Center
                  Positioned(
                    top: avatarMarginTop,
                    left: (MediaQuery.of(context).size.width / 2) - (avatarSize / 2),
                    child: Container(
                      height: avatarSize,
                      width: avatarSize,
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            offset: const Offset(0, 4),
                            blurRadius: 4,
                            spreadRadius: 0,
                            color: AppColors.black.withOpacity2(0.15),
                          ),
                        ],
                      ),
                      child: Image.asset(AppAssets.userAvtarImage, fit: BoxFit.fill),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
