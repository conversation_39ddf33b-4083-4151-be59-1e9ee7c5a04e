part of 'i_local_storage_repository.dart';

@Injectable(as: ILocalStorageRepository)
class LocalStorageRepository extends ILocalStorageRepository {
  LocalStorageRepository(super.preferences);

  @override
  Future<bool> setToken(String? value) async {
    if (value == null) return false;
    try {
      await preferences.setString(AppStrings.tokenKey, value);
      return true;
    } on Exception catch (e, s) {
      log('Error Set Token: $e \n $s');
      return false;
    }
  }

  @override
  String? get token => preferences.getString(AppStrings.tokenKey);

  @override
  Future<bool> clearAuth() async {
    try {
      final isRemoveToken = await preferences.remove(AppStrings.tokenKey);
      final isRemoveUser = await preferences.remove(AppStrings.userPrefKey);
      return isRemoveUser && isRemoveToken;
    } on Exception catch (e, s) {
      log('Error Clear Auth: $e \n $s');
      return false;
    }
  }

  @override
  Future<UserModel> setUser(UserModel? user) async {
    try {
      final isSuccess = await preferences.setString(AppStrings.userPrefKey, jsonEncode(user));
      if (!isSuccess) {
        throw Exception('Failed to save user data');
      }
      return user!;
    } catch (e) {
      throw Exception('Error saving user: ${e.toString()}');
    }
  }

  @override
  UserModel? get getUser {
    try {
      final userKey = preferences.getString(AppStrings.userPrefKey);
      if (userKey != null) {
        final user = UserModel.fromJson(jsonDecode(userKey) as Map<String, dynamic>);
        return user;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
