import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/icon_text_widget.dart';
import 'package:leiuniverse/widget/user_info_widget.dart';

class EventPromotionWidget extends StatelessWidget {
  const EventPromotionWidget({super.key, this.onTap, this.onEditTap, this.onDeleteTap, this.isFromMyEvent = false});
  final void Function()? onTap;
  final void Function()? onEditTap;
  final void Function()? onDeleteTap;
  final bool isFromMyEvent;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  4,
                  (index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: '',
                        placeholder: AppAssets.placeholderImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 147,
                        width: 236,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
              ),
            ),
            const Gap(14),
            Text(
              'Event name here',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(8),
            Row(
              children: [
                const AppSvgImage(
                  AppAssets.locationIcon,
                ),
                const Gap(6),
                Text(
                  'Vadodara, Gujarat, India',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            const Gap(14),
            const Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconTextWidget(
                  icon: AppAssets.dislikeHandIcon,
                  text: '100 Likes',
                ),
                Gap(14),
                IconTextWidget(
                  icon: AppAssets.messageIcon,
                  text: '123 Comments',
                ),
              ],
            ),
            const Gap(14),
            const Divider(
              height: 0,
            ),
            const Gap(14),
            const UserInfoWidget(
              name: 'John Doe',
              businessName: 'Business name here',
              imageUrl: AppAssets.userAvtarImage,
            ),
            if (isFromMyEvent) ...[
              const Gap(12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      'Valid till 22/06/2025',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.primary,
                          ),
                    ),
                  ),
                  InkWell(
                    onTap: onEditTap,
                    child: const AppSvgImage(AppAssets.editIcon),
                  ),
                  const Gap(16),
                  InkWell(
                    onTap: onDeleteTap,
                    child: const AppSvgImage(AppAssets.deleteIcon),
                  ),
                ],
              )
            ]
          ],
        ),
      ),
    );
  }
}
