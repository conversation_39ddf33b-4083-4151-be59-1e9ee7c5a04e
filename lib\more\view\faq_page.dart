import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class FaqPage extends StatefulWidget {
  const FaqPage({super.key});

  @override
  State<FaqPage> createState() => _FaqPageState();
}

class _FaqPageState extends State<FaqPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'FAQ’s',
      ),
      body: ListView.builder(
        itemCount: 8,
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
        itemBuilder: (context, index) {
          return faqView(
            title: 'Personal Data processed for the following purposes',
            content:
                'STERYLAB has devoted its efforts to the production of medical needles and devices for over 45 years, keeping its development in step with the constant progress in medicine and the latest advances in technology.',
          );
        },
      ),
    );
  }

  Widget faqView({required String title, required String content}) {
    return Theme(
      data: Theme.of(context).copyWith(
        unselectedWidgetColor: AppColors.subText,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ExpansionTile(
            tilePadding: EdgeInsets.zero,
            childrenPadding: const EdgeInsets.symmetric(vertical: 8),
            dense: true,
            enabled: true,
            iconColor: AppColors.subText,
            title: Text(
              title,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            children: [
              Text(
                content,
                style: Theme.of(context).textTheme.titleSmall,
              )
            ],
          ),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Divider(
              height: 1,
              color: AppColors.border,
            ),
          )
        ],
      ),
    );
  }
}
