import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/common_button.dart';

class DeleteProfileView extends StatelessWidget {
  const DeleteProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Delete Account',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Gap(4),
          Text(
            'Are you sure you want to delete?',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Gap(4),
          Text(
            'You can not undo this action',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.primary),
          ),
          const Gap(30),
          Row(
            children: [
              Expanded(
                child: CommonButton(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  text: 'No',
                ),
              ),
              const Gap(15),
              Expanded(
                child: CommonButton(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  text: 'Yes, Delete',
                  removeShadow: true,
                  backgroundColor: AppColors.background,
                  textColor: AppColors.ebonyClay,
                  borderColor: AppColors.background,
                ),
              ),
            ],
          ),
          const Gap(20),
        ],
      ),
    );
  }
}
