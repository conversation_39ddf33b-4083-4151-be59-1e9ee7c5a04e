// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';

class AppTextForm<PERSON>ield extends StatelessWidget {
  const AppTextFormField({
    super.key,
    this.controller,
    this.title,
    this.titleColor,
    this.hintText,
    this.enabled,
    this.validator,
    this.suffixIcon,
    this.prefixIcon,
    this.obscureText = false,
    this.readOnly = false,
    this.maxLines = 1,
    this.textInputAction,
    this.contentPadding,
    this.onFieldSubmitted,
    this.onTap,
    this.onChanged,
    this.obscuringCharacter,
    this.maxWidth,
    this.maxHeight,
    this.fillColor,
    this.inputFormatters,
    this.style,
    this.keyboardType,
    this.hintStyle,
    this.focusNode,
    this.errorMaxLines,
    this.border,
  });
  final TextEditingController? controller;
  final String? title;
  final Color? titleColor;
  final String? hintText;
  final bool? enabled;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final bool obscureText;
  final bool readOnly;
  final int maxLines;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final void Function(String)? onFieldSubmitted;
  final void Function()? onTap;
  final void Function(String)? onChanged;
  final String? obscuringCharacter;
  final double? maxWidth;
  final double? maxHeight;
  final Color? fillColor;
  final List<TextInputFormatter>? inputFormatters;
  final TextStyle? style;
  final TextInputType? keyboardType;
  final TextStyle? hintStyle;
  final FocusNode? focusNode;
  final int? errorMaxLines;
  final InputBorder? border;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(title!.inCaps,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: titleColor ?? AppColors.text,
                  )),
          const Gap(6),
        ],
        TextFormField(
          controller: controller,
          enabled: enabled,
          validator: validator,
          obscureText: obscureText,
          obscuringCharacter: obscuringCharacter ?? '•',
          maxLines: maxLines,
          textInputAction: textInputAction,
          onFieldSubmitted: onFieldSubmitted,
          readOnly: readOnly,
          focusNode: focusNode,
          inputFormatters: inputFormatters,
          keyboardType: keyboardType,
          style: style,
          onTap: onTap,
          onChanged: onChanged,
          onTapOutside: (event) {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          decoration: InputDecoration(
            isDense: true,
            constraints: BoxConstraints(
              maxWidth: maxWidth ?? 400,
              maxHeight: maxHeight ?? double.infinity,
            ),
            hintText: hintText,
            fillColor: fillColor,
            border: border,
            filled: true,
            suffixIcon: suffixIcon,
            errorMaxLines: errorMaxLines,
            prefixIcon: prefixIcon,
            hintStyle: hintStyle,
            contentPadding: contentPadding,
          ),
        ),
      ],
    );
  }
}
