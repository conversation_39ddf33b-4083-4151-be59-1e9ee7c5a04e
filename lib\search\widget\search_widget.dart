import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class SearchWidget extends StatelessWidget {
  const SearchWidget({super.key, this.searchString});
  final String? searchString;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 11),
          child: Row(
            children: [
              AppSvgImage(
                searchString?.isEmpty ?? true ? AppAssets.clockIcon : AppAssets.searchIcon,
                height: 20,
                width: 20,
                color: AppColors.subText,
              ),
              const Gap(11),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Jenny',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppColors.black,
                          ),
                    ),
                    Text(
                      'Business name here if available',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                            color: AppColors.subText,
                          ),
                    ),
                  ],
                ),
              ),
              if (searchString?.isNotEmpty ?? true) const AppSvgImage(AppAssets.rightArrowIconIos),
            ],
          ),
        ),
        const Divider(
          height: 0,
        )
      ],
    );
  }
}
