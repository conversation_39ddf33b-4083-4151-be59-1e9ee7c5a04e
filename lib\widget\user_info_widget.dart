import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class UserInfoWidget extends StatelessWidget {
  const UserInfoWidget({super.key, this.name, this.businessName, this.imageUrl});
  final String? name;
  final String? businessName;
  final String? imageUrl;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Utility.imageLoader(
          url: '',
          placeholder: AppAssets.userAvtarImage,
          height: 36,
          width: 36,
          fit: BoxFit.cover,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
              color: AppColors.black.withOpacity2(0.15),
            ),
          ],
        ),
        const Gap(14),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    name ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Gap(3),
                  const AppSvgImage(
                    AppAssets.verifyIcon,
                    height: 17,
                    width: 17,
                  ),
                ],
              ),
              Text(
                businessName ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: AppColors.subText,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
