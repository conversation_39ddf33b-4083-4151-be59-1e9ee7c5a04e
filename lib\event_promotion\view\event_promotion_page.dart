import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/event_promotion/view/create_event_promotion_page.dart';
import 'package:leiuniverse/event_promotion/view/event_promotion_details_page.dart';
import 'package:leiuniverse/event_promotion/view/my_event_promotion_page.dart';
import 'package:leiuniverse/event_promotion/widget/event_promotion_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class EventPromotionPage extends StatefulWidget {
  const EventPromotionPage({super.key});

  @override
  State<EventPromotionPage> createState() => _EventPromotionPageState();
}

class _EventPromotionPageState extends State<EventPromotionPage> {
  final searchController = TextEditingController();
  final eventPromotionList = ValueNotifier<List<EventPromotionWidget>>([
    const EventPromotionWidget(),
    const EventPromotionWidget(),
    const EventPromotionWidget(),
  ]);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Event/Promotion',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: [
          CommonButton(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MyEventPromotionPage(),
                ),
              );
            },
            fontSize: 14,
            text: 'My Events',
            removeShadow: true,
            width: 83,
            padding: const EdgeInsets.symmetric(
              vertical: 4,
            ),
            margin: const EdgeInsets.only(right: 24, top: 13, bottom: 13),
            borderRadius: 6,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            AppTextFormField(
              controller: searchController,
              hintText: 'Search',
              fillColor: AppColors.white,
              prefixIcon: const Padding(
                padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                child: AppSvgImage(
                  AppAssets.searchIcon,
                  color: AppColors.subText,
                ),
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: eventPromotionList,
                      builder: (context, eventPromotion, _) {
                        return ListView.separated(
                          separatorBuilder: (context, index) => const Gap(20),
                          itemCount: eventPromotion.length,
                          shrinkWrap: true,
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return EventPromotionWidget(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const EventPromotionDetailsPage(),
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: CommonButton(
        width: 141,
        borderRadius: 200,
        fontSize: 15,
        height: 40,
        padding: EdgeInsets.zero,
        isLessShadow: true,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateEventPromotionPage(),
            ),
          );
        },
        icon: const AppSvgImage(
          AppAssets.postEventIcon,
        ),
        text: 'Post Event',
      ),
    );
  }
}
