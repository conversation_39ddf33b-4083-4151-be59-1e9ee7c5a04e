import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';

class LightAppTypography {
  static const _fontFamily = 'SFProDisplay';

  static const _style = TextStyle(fontFamily: _fontFamily, color: AppColors.text);

  static TextStyle headlineLarge = _style.copyWith(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    letterSpacing: 1,
    height: 1.5,
  ); // H1

  static TextStyle headlineMedium = _style.copyWith(
    fontSize: 18,
    fontWeight: FontWeight.w700,
    letterSpacing: 0.5,
    height: 1.3,
  ); // H2

  static TextStyle headlineSmall = _style.copyWith(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.5,
  ); // H3

  static TextStyle titleLarge = _style.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 1,
    height: 1.6,
  ); // Body 0 (Regular)

  static TextStyle titleMedium = _style.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 1,
    height: 1.6,
  ); // Body 0 (Medium)

  static TextStyle titleSmall = _style.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w400,
    letterSpacing: 1,
    height: 1.6,
  ); // Body 1 (Regular)

  static TextStyle bodyLarge = _style.copyWith(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    letterSpacing: 1,
    height: 1.6,
  ); // Body 1 (Medium)

  static TextStyle bodyMedium = _style.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 1,
    height: 1.57,
  ); // Body 2 (Regular)

  static TextStyle bodySmall = _style.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 1,
    height: 1.57,
  ); // Body 2 (Medium)

  static TextStyle labelLarge = _style.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 1,
    height: 1.5,
  ); // Caption (Regular)

  static TextStyle labelMedium = _style.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 1.5,
    height: 1.6,
  ); // button

  static TextStyle labelSmall = _style.copyWith(
    fontSize: 10,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.5,
  );
}
