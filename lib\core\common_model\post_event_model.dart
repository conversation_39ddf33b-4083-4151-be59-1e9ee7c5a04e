// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';

class PostOrEventModel {
  final String? title;
  final String? eventName;
  final String? location;
  final String? description;
  final String? price;
  final String? contactNumber;
  final String? category;
  final String? subCategory;
  final String? childSubCategory;
  final String? country;
  final String? state;
  final String? city;
  final String? duration;
  final List<PickedFileModel>? images;

  PostOrEventModel(
      {this.title,
      this.eventName,
      this.location,
      this.description,
      this.price,
      this.contactNumber,
      this.category,
      this.subCategory,
      this.childSubCategory,
      this.country,
      this.state,
      this.city,
      this.duration,
      this.images});

  PostOrEventModel copyWith({
    String? title,
    String? eventName,
    String? location,
    String? description,
    String? price,
    String? contactNumber,
    String? category,
    String? subCategory,
    String? childSubCategory,
    String? country,
    String? state,
    String? city,
    String? duration,
    List<PickedFileModel>? images,
  }) {
    return PostOrEventModel(
      title: title ?? this.title,
      eventName: eventName ?? eventName,
      location: location ?? location,
      description: description ?? this.description,
      price: price ?? this.price,
      contactNumber: contactNumber ?? this.contactNumber,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      childSubCategory: childSubCategory ?? this.childSubCategory,
      country: country ?? this.country,
      state: state ?? this.state,
      city: city ?? this.city,
      duration: duration ?? this.duration,
      images: images ?? this.images,
    );
  }
}
