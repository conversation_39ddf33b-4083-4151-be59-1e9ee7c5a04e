import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/market_place/view/target_buyers_view.dart';
import 'package:leiuniverse/setup_business/view/business_image_view.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class CreateEventPromotionPage extends StatefulWidget {
  const CreateEventPromotionPage({super.key});

  @override
  State<CreateEventPromotionPage> createState() => _CreateEventPromotionPageState();
}

class _CreateEventPromotionPageState extends State<CreateEventPromotionPage> {
  final _formKey = GlobalKey<FormState>();
  final adTitleController = TextEditingController();
  final adDescriptionController = TextEditingController();
  final priceController = TextEditingController();
  final contactNumberController = TextEditingController();
  final createEventModel = ValueNotifier<PostOrEventModel>(PostOrEventModel());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Create Event/Promotion',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: ValueListenableBuilder(
              valueListenable: createEventModel,
              builder: (context, postAdValues, _) {
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Images of Event - 2 max',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.primary,
                                ),
                          ),
                          const Gap(20),
                          BusinessImageView(
                            maxImage: 2,
                            onImageSelected: (images) {
                              createEventModel.value = createEventModel.value.copyWith(
                                images: images,
                              );
                            },
                          ),
                          const Gap(20),
                          AppTextFormField(
                            title: 'Event name',
                            controller: adTitleController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter event name';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              createEventModel.value = createEventModel.value.copyWith(
                                eventName: value,
                              );
                            },
                          ),
                          const Gap(14),
                          AppTextFormField(
                            title: 'Location',
                            controller: adTitleController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter location';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              createEventModel.value = createEventModel.value.copyWith(
                                location: value,
                              );
                            },
                          ),
                          const Gap(14),
                          AppTextFormField(
                            title: 'Short description',
                            controller: adDescriptionController,
                            maxLines: 4,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a description';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              createEventModel.value = createEventModel.value.copyWith(
                                description: value,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetBuyersView(
                      title: 'Target Audience (optional)',
                      postOrEventModel: createEventModel.value,
                      onModelUpdated: (targetBuyers) {
                        createEventModel.value = createEventModel.value.copyWith(
                          category: targetBuyers.category,
                          subCategory: targetBuyers.subCategory,
                          childSubCategory: targetBuyers.childSubCategory,
                        );
                      },
                    ),
                    Container(
                      padding: const EdgeInsets.all(10),
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      decoration: BoxDecoration(
                        color: AppColors.offGreen,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        AppStrings.eventPromotionAutoExpire,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 13,
                            ),
                      ),
                    ),
                    const Gap(30),
                    CommonButton(
                      text: 'Create Event',
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      onTap: () {
                        log(postAdValues.images?.length.toString() ?? '0');
                        // if (_formKey.currentState!.validate()) {}
                      },
                    ),
                    const Gap(30),
                  ],
                );
              }),
        ),
      ),
    );
  }
}
