import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/view/write_comment_view.dart';
import 'package:leiuniverse/event_promotion/widget/comment_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/icon_text_widget.dart';
import 'package:leiuniverse/widget/user_info_widget.dart';

class EventPromotionDetailsPage extends StatefulWidget {
  const EventPromotionDetailsPage({super.key});

  @override
  State<EventPromotionDetailsPage> createState() => _EventPromotionDetailsPageState();
}

class _EventPromotionDetailsPageState extends State<EventPromotionDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(
                    4,
                    (index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 14),
                        child: Utility.imageLoader(
                          url: '',
                          placeholder: AppAssets.placeholderImage,
                          height: 218,
                          width: 323,
                          fit: BoxFit.cover,
                        ),
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                top: 40,
                left: 20,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const AppSvgImage(
                    AppAssets.roundBackIcon,
                  ),
                ),
              ),
              const Positioned(
                top: 40,
                right: 20,
                child: Row(
                  children: [
                    AppSvgImage(
                      AppAssets.roundShareIcon,
                    ),
                    Gap(20),
                    AppSvgImage(
                      AppAssets.roundMenuIcon,
                    ),
                  ],
                ),
              ),
            ],
          ),
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsetsGeometry.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Event name here',
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                        const Gap(8),
                        Row(
                          children: [
                            const AppSvgImage(
                              AppAssets.locationIcon,
                            ),
                            const Gap(6),
                            Text(
                              'Vadodara, Gujarat, India',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const Gap(14),
                        const Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            IconTextWidget(
                              icon: AppAssets.dislikeHandIcon,
                              text: '100 Likes',
                            ),
                            Gap(14),
                            IconTextWidget(
                              icon: AppAssets.messageIcon,
                              text: '123 Comments',
                            ),
                          ],
                        ),
                        const Gap(20),
                        Container(
                          padding: const EdgeInsets.all(14),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: AppColors.background,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Description'.allInCaps,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.subText,
                                    ),
                              ),
                              const Gap(10),
                              Text(
                                AppStrings.eventPromotionAutoExpire,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                        const Gap(20),
                        const UserInfoWidget(
                          name: 'John Doe',
                          businessName: 'Business Name',
                          imageUrl: AppAssets.userAvtarImage,
                        ),
                      ],
                    ),
                  ),
                  const Divider(
                    height: 0,
                    thickness: 4,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Comments',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: AppColors.primary,
                              ),
                        ),
                        const Gap(14),
                        Flexible(
                          child: ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: 5,
                            padding: EdgeInsets.zero,
                            separatorBuilder: (context, index) {
                              return const Gap(20);
                            },
                            itemBuilder: (context, index) {
                              return const CommentWidget();
                            },
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
      bottomSheet: BottomButtonWidget(
        onTap: () {
          showModalBottomSheet(
            context: context,
            backgroundColor: AppColors.white,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            builder: (context) {
              return const WriteCommentView();
            },
          );
        },
        text: 'Add Comment',
      ),
    );
  }
}
