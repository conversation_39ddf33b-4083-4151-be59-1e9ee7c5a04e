import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/onbording/view/onbording_1_page.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    timer();
  }

  void timer() {
    Timer(
      const Duration(seconds: 3),
      () {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => const Onbording1Page(), //  const Onbording1Page(),
          ),
          (route) => false,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  AppAssets.logo,
                  height: 100,
                  width: 100,
                ),
                const Gap(15),
                Text(
                  'LEI-Universe',
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
                Text(
                  'Live Event Industry',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 80,
            child: Image.asset(
              AppAssets.loading,
              color: AppColors.subText,
              width: 44,
            ),
          )
        ],
      ),
    );
  }
}
