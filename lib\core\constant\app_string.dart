class AppStrings {
  static const storageUrl = '';
  static const tokenKey = 'token';
  static const userPrefKey = 'user';
  static const login = '/api/login';
  static const signUp = '/api/register';
  static const forgotPassword = '/api/forgot-password';
  static const verifyOtp = '/api/verify-otp';
  static const updateProfile = '/api/update-profile';
  static const resetPassword = '/api/reset-password';
  static const logout = '/api/logout';
  static const deleteAccount = '/api/delete-account';
  static userDetail(id) => '/api/user-detail$id';

  // App Titles
  static const appName = 'LEI-Universe';

  // Buttons
  static const ok = 'OK';
  static const cancel = 'Cancel';
  static const submit = 'Submit';

  // Error Messages
  static const errorSomethingWentWrong = 'Something went wrong. Please try again.';
  static const errorNoInternet = 'No internet connection.';

  // Home Page
  static const homePageTagline = 'Discover Talent, Events and Opportunities — All in One Place!';

  // Labels
  static const email = 'Email';
  static const password = 'Password';

  // Event Promotion
  static const eventPromotionAutoExpire = 'The post of event/ promotion will automatically expire in 30 days.';

  // Other
  static const loading = 'Loading...';
  static const logoutConfirmation = 'Are you sure you want to logout?';
  static const visitorLoginText =
      'Interested members can register for app info and agency searches. You can join group chats, make inquiries, sell items, and search for agency members. Registration as a visitor  member is free.';
  static const bussinessLoginText =
      'Agency  can create up to 1 to 4 different id by registering in different categories of different agencies. For this, interested member has pay ₹150 for one id generation per year, and ₹100 for each add on id.';
  static const bussinessSignupText =
      'Agency  can create up to 1 to 4 different id by registering in different categories of different agencies. For this, interested member has pay ₹100 for one id generation per year, and ₹50 for each add on id.';
}
