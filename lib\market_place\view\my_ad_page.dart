import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/market_place/view/post_ad_page.dart';
import 'package:leiuniverse/market_place/widget/my_ad_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class MyAdPage extends StatefulWidget {
  const MyAdPage({super.key});

  @override
  State<MyAdPage> createState() => _MyAdPageState();
}

class _MyAdPageState extends State<MyAdPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'My Ads',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ListView.separated(
        itemCount: 10,
        padding: const EdgeInsets.all(20),
        separatorBuilder: (context, index) => const Gap(20),
        itemBuilder: (context, index) {
          return MyAdWidget(
            onTap: () {},
            onMessageTap: () {},
            onEditTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PostAdPage(),
                ),
              );
            },
            onDeleteTap: () {},
          );
        },
      ),
    );
  }
}
