import 'dart:io';

import 'package:flutter/material.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/photo_frame_widget.dart';

class BusinessImageView extends StatefulWidget {
  const BusinessImageView({super.key, this.maxImage, required this.onImageSelected});
  final int? maxImage;
  final Function(List<PickedFileModel>) onImageSelected;

  @override
  State<BusinessImageView> createState() => _BusinessImageViewState();
}

class _BusinessImageViewState extends State<BusinessImageView> {
  final images = ValueNotifier<List<PickedFileModel>>([]);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<PickedFileModel>>(
      valueListenable: images,
      builder: (context, listOfImages, _) {
        return SizedBox(
          height: 153,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                PhotoFrameWidget(
                  onTap: () async {
                    if (widget.maxImage != null && listOfImages.length >= widget.maxImage!) {
                      Utility.toast(message: 'Maximum ${widget.maxImage} images allowed.');
                      return;
                    }

                    final hasPermission = await Utility().requestGalleryPermission();
                    if (!hasPermission) {
                      // Utility.toast(message: 'Please grant photo access permission.');
                      return;
                    }

                    final pickedMobileImage = await Utility.pickMultiImageNew();

                    final remaining = (widget.maxImage ?? pickedMobileImage.length) - listOfImages.length;
                    final imagesToAdd = pickedMobileImage.take(remaining);

                    if (imagesToAdd.isNotEmpty) {
                      images.value = [
                        ...listOfImages,
                        ...imagesToAdd.map(
                          (e) => PickedFileModel(
                            fileExtension: e.extension ?? 'png',
                            file: File(e.path!),
                          ),
                        ),
                      ];
                    }
                    widget.onImageSelected(images.value);
                  },
                ),
                ...listOfImages.map((img) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 23),
                    child: PhotoFrameWidget(
                      images: img,
                      onRemove: () {
                        final updatedList = List<PickedFileModel>.from(images.value)..remove(img);
                        images.value = updatedList;
                        widget.onImageSelected(updatedList);
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }
}
