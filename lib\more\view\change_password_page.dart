import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final currentPassObscure = ValueNotifier<bool>(false);
  final newPassObscure = ValueNotifier<bool>(false);
  final confirmPassObscure = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Change Password',
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ValueListenableBuilder<bool>(
                  valueListenable: currentPassObscure,
                  builder: (context, obscure, _) {
                    return AppTextFormField(
                      controller: currentPasswordController,
                      title: 'Current Password',
                      hintText: 'Password',
                      obscureText: !obscure,
                      textInputAction: TextInputAction.next,
                      suffixIcon: IconButton(
                        onPressed: () {
                          currentPassObscure.value = !obscure;
                        },
                        icon: AppSvgImage(
                          !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter current password';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const Gap(20),
                ValueListenableBuilder<bool>(
                  valueListenable: newPassObscure,
                  builder: (context, obscure, _) {
                    return AppTextFormField(
                      controller: newPasswordController,
                      title: 'New Password',
                      hintText: 'Password',
                      obscureText: !obscure,
                      textInputAction: TextInputAction.next,
                      suffixIcon: IconButton(
                        onPressed: () {
                          newPassObscure.value = !obscure;
                        },
                        icon: AppSvgImage(
                          !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter new password';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const Gap(20),
                ValueListenableBuilder<bool>(
                  valueListenable: confirmPassObscure,
                  builder: (context, obscure, _) {
                    return AppTextFormField(
                      controller: confirmPasswordController,
                      title: 'Confirm Password',
                      hintText: 'Password',
                      obscureText: !obscure,
                      textInputAction: TextInputAction.done,
                      suffixIcon: IconButton(
                        onPressed: () {
                          confirmPassObscure.value = !obscure;
                        },
                        icon: AppSvgImage(
                          !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter confirm password';
                        }
                        if (value != newPasswordController.text) {
                          return 'Password does not match';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const Gap(80),
              ],
            ),
          ),
        ),
      ),
      bottomSheet: BottomButtonWidget(
        onTap: () {
          if (formKey.currentState!.validate()) {
            Navigator.pop(context);
          }
        },
        text: 'Change Password',
      ),
    );
  }
}
