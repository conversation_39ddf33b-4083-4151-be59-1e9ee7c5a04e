import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/profile/widget/profile_category_view.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ProfileUserDetail extends StatelessWidget {
  const ProfileUserDetail({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Jenny',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(height: 1.2),
            ),
            const Gap(5),
            const AppSvgImage(
              AppAssets.verifyIcon,
              height: 22,
              width: 22,
            )
          ],
        ),
        Text(
          'Business name here',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.subText),
        ),
        const Gap(7),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100),
            color: AppColors.background,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppSvgImage(AppAssets.locationIcon, height: 18, width: 18),
              const Gap(2),
              Text(
                'Vadodara, Gujarat, India',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.2),
              )
            ],
          ),
        ),
        const Gap(24),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {},
              child: const AppSvgImage(
                AppAssets.roundFavIcon,
                height: 42,
                width: 42,
              ),
            ),
            const Gap(24),
            callButton(context: context, onTap: () {}),
            const Gap(24),
            InkWell(
              onTap: () {},
              child: const AppSvgImage(
                AppAssets.roundMessageIcon,
                height: 42,
                width: 42,
              ),
            ),
          ],
        ),
        const Gap(24),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const AppSvgImage(AppAssets.dislikeHandIcon, height: 24, width: 24),
                const Gap(6),
                Text(
                  '100 Likes',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                )
              ],
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 18),
              height: 54,
              width: 1,
              color: AppColors.porcelain,
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const AppSvgImage(AppAssets.facebookIcon, height: 24, width: 24),
                const Gap(6),
                Text(
                  'Facebook',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                )
              ],
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 18),
              height: 54,
              width: 1,
              color: AppColors.porcelain,
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const AppSvgImage(AppAssets.instagramIcon, height: 24, width: 24),
                const Gap(6),
                Text(
                  'Instagram',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                )
              ],
            ),
          ],
        ),
        const Gap(24),
        const Divider(
          height: 4,
          thickness: 4,
          color: AppColors.porcelain,
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('BUSINESS / SERVICES',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.subText)),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 3,
                padding: const EdgeInsets.only(top: 20),
                itemBuilder: (context, index) {
                  return const ProfileCategoryView();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget callButton({required BuildContext context, void Function()? onTap}) {
    return InkWell(
      onTap: () {},
      child: Container(
        height: 42,
        width: 118,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100),
          color: AppColors.primary,
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity2(0.25),
              spreadRadius: 0,
              blurRadius: 14,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppSvgImage(
              AppAssets.callIcon,
              height: 24,
              width: 24,
            ),
            const Gap(6),
            Text(
              'Call',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.white, height: 1.2),
            )
          ],
        ),
      ),
    );
  }
}
