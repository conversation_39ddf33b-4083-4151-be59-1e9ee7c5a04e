class ImageModel {
  int? id;
  int? userId;
  String? filePath;
  String? fileType;
  String? createdAt;
  String? updatedAt;
  String? fileUrl;

  ImageModel({this.id, this.userId, this.filePath, this.fileType, this.createdAt, this.updatedAt, this.fileUrl});

  ImageModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    filePath = json['file_path'];
    fileUrl = json['file_url'];
    fileType = json['file_type'] ?? 'NETWORK';
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['file_path'] = filePath;
    data['file_url'] = fileUrl;
    data['file_type'] = fileType;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
