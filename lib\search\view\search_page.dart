import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/search/widget/search_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final searchController = TextEditingController();
  final searchValue = ValueNotifier('');

  @override
  void dispose() {
    searchController.dispose();
    searchValue.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: ValueListenableBuilder<String>(
          valueListenable: searchValue,
          builder: (context, value, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  color: AppColors.inputField,
                  child: AppTextFormField(
                    controller: searchController,
                    hintText: 'Search by business, user...',
                    onChanged: (val) => searchValue.value = val,
                    suffixIcon: value.isNotEmpty
                        ? GestureDetector(
                            onTap: () {
                              searchController.clear();
                              searchValue.value = '';
                            },
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 18, horizontal: 17),
                              child: AppSvgImage(AppAssets.closeIcon),
                            ),
                          )
                        : null,
                    prefixIcon: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 17),
                      child: InkWell(
                        onTap: () => Navigator.pop(context),
                        child: const AppSvgImage(AppAssets.leftArrowIcon),
                      ),
                    ),
                  ),
                ),
                Flexible(
                  child: ListView.builder(
                    itemCount: 20,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return SearchWidget(searchString: value);
                    },
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }
}
