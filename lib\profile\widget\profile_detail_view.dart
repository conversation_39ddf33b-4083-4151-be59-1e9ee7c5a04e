import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';

class ProfileDetailView extends StatelessWidget {
  const ProfileDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                height: 76,
                width: 76,
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: AppColors.black.withOpacity2(0.15),
                    ),
                  ],
                ),
                child: Image.asset(AppAssets.userAvtarImage, fit: BoxFit.fill),
              ),
              const Gap(12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Jenny',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(height: 1.2),
                      ),
                      const Gap(5),
                      const AppSvgImage(
                        AppAssets.verifyIcon,
                        height: 18,
                        width: 18,
                      ),
                    ],
                  ),
                  Text(
                    'Business name here',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const AppSvgImage(
                        AppAssets.dislikeHandIcon,
                        height: 20,
                        width: 20,
                      ),
                      const Gap(6),
                      Text(
                        '550 Likes',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          const Gap(20),
          personalInfoView(context: context, title: 'Mobile', value: '1234567890'),
          const Gap(20),
          personalInfoView(context: context, title: 'Email', value: '<EMAIL>'),
          const Gap(20),
          personalInfoView(context: context, title: 'Address', value: 'Vadodara, Gujarat, India'),
          const Gap(20),
          personalInfoView(context: context, title: 'Instagram', icon: AppAssets.instagramIcon),
          const Gap(20),
          personalInfoView(context: context, title: 'Facebook', icon: AppAssets.facebookIcon),
          const Gap(20),
          personalInfoView(context: context, title: 'Profile Status', value: 'Verified'),
          const Gap(8),
          AppTextFormField(
            maxLines: 2,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 13),
            controller: TextEditingController(
                text: 'When you get 500+ likes on your profile, your account receives a verification badge.'),
          ),
        ],
      ),
    );
  }

  Widget personalInfoView({required BuildContext context, String? title, String? value, String? icon}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            title ?? '',
            maxLines: 1,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.subText),
          ),
        ),
        const Gap(10),
        if (value != null)
          Flexible(
            child: Text(
              value,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
        if (icon != null) AppSvgImage(icon),
      ],
    );
  }
}
