import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ProfileCategoryView extends StatelessWidget {
  const ProfileCategoryView({super.key, this.isEditable = false});
  final bool isEditable;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(14),
      margin: isEditable ? null : const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.offGreen,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Musician',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Gap(10),
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.start,
            runSpacing: 10,
            spacing: 10,
            children: List.generate(
              3,
              (index) {
                return const CategoryTypeWidget(
                  title: 'Keyboard',
                  color: AppColors.turquoiseGreen,
                );
              },
            ),
          ),
          const Gap(20),
          Text(
            "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,",
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const Gap(14),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                4,
                (index) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 14),
                    child: Utility.imageLoader(
                      url: '',
                      placeholder: AppAssets.whiteBgImage,
                      borderRadius: BorderRadius.circular(8),
                      height: 93,
                      width: 93,
                      fit: BoxFit.cover,
                    ),
                  );
                },
              ),
            ),
          ),
          if (isEditable) ...[
            const Gap(14),
            const Divider(
              height: 1,
              thickness: 1,
              color: AppColors.subText,
            ),
            const Gap(14),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Valid till 22/06/2025',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                      ),
                      Text(
                        'trxn id : 11211211',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                      )
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {},
                  child: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: AppSvgImage(AppAssets.editIcon),
                  ),
                ),
                InkWell(
                  onTap: () {},
                  child: const Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: AppSvgImage(AppAssets.deleteIcon),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
