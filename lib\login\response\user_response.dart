import 'package:equatable/equatable.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class UserResponse extends Equatable {
  const UserResponse({
    required this.data,
    required this.token,
    required this.message,
    required this.status,
    this.transactionId,
  });

  final UserModel? data;
  final String? token;
  final String? transactionId;
  final String? message;
  final String? status;

  factory UserResponse.fromJson(Map<String, dynamic> json) {
    return UserResponse(
      data: json["data"] == null ? null : UserModel.fromJson(json["data"]),
      token: json["token"],
      message: json["message"],
      status: json["status"],
      transactionId: json["transaction_id"],
    );
  }

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "token": token,
        "message": message,
        "status": status,
        "transaction_id": transactionId,
      };

  @override
  List<Object?> get props => [
        data,
        token,
        message,
        status,
        transactionId,
      ];
}
