import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page.dart';
import 'package:leiuniverse/signup/view/verification_page.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/photo_frame_widget.dart';

class VisitorRegisterView extends StatefulWidget {
  const VisitorRegisterView({super.key});

  @override
  State<VisitorRegisterView> createState() => _VisitorRegisterViewState();
}

class _VisitorRegisterViewState extends State<VisitorRegisterView> {
  final nameController = TextEditingController();
  final mobileController = TextEditingController();
  final emailController = TextEditingController();
  final obscureText = ValueNotifier<bool>(false);
  final passwordController = TextEditingController();
  final pincodeController = TextEditingController();

  final countryList = ['US', 'India', 'Japan'];
  final stateList = ['Gujarat', 'UP', 'Rajasthan'];
  final cityList = ['Surat', 'Mumbai', 'Pune'];

  final selectedCity = ValueNotifier<String?>(null);
  final selectedState = ValueNotifier<String?>(null);
  final selectedCountry = ValueNotifier<String?>(null);
  final termsAccepted = ValueNotifier<bool>(false);
  final pickedImageFile = ValueNotifier<PickedFileModel?>(null);
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppColors.offGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    AppStrings.visitorLoginText,
                    style:
                        Theme.of(context).textTheme.labelLarge?.copyWith(fontSize: 13, letterSpacing: 1.1, height: 1.5),
                  ),
                ),
                const Gap(30),
                ValueListenableBuilder<PickedFileModel?>(
                    valueListenable: pickedImageFile,
                    builder: (context, file, _) {
                      return PhotoFrameWidget(
                        onTap: () async {
                          final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                          if (pickedImage != null) {
                            pickedImageFile.value = PickedFileModel(file: File(pickedImage.path));
                          }
                        },
                        onRemove: () {
                          pickedImageFile.value = null;
                        },
                        images: file,
                      );
                    }),
                const Gap(30),
                AppTextFormField(
                  title: 'Name',
                  controller: nameController,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter name';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                AppTextFormField(
                  title: 'Mobile',
                  controller: mobileController,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter mobile number';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                AppTextFormField(
                  title: 'Email',
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter email';
                    } else if (!Utility.isValidEmail(value)) {
                      return 'Please enter valid email';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                ValueListenableBuilder<bool>(
                    valueListenable: obscureText,
                    builder: (context, obscure, _) {
                      return AppTextFormField(
                        controller: passwordController,
                        title: 'Password',
                        hintText: 'Password',
                        obscureText: !obscure,
                        suffixIcon: IconButton(
                          onPressed: () {
                            obscureText.value = !obscure;
                          },
                          icon: AppSvgImage(
                            !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter password';
                          }
                          return null;
                        },
                      );
                    }),
              ],
            ),
          ),
          const Divider(
            height: 1,
            color: AppColors.border,
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Address',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                ),
                const Gap(14),
                Row(
                  children: [
                    Expanded(
                      child: AppTextFormField(
                        title: 'Pincode',
                        controller: pincodeController,
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter pincode';
                          }
                          return null;
                        },
                      ),
                    ),
                    const Gap(15),
                    Expanded(
                      child: ValueListenableBuilder<String?>(
                          valueListenable: selectedCity,
                          builder: (context, city, _) {
                            return AppDropDown(
                              title: 'City',
                              onSelect: (value) {
                                selectedCity.value = value;
                              },
                              selectedValue: city,
                              items: cityList
                                  .map((e) => DropdownMenuItem<String>(
                                        value: e,
                                        child: Text(
                                          e,
                                        ),
                                      ))
                                  .toList(),
                              hintText: 'Select',
                              hintTextColor: AppColors.text,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select city';
                                }
                                return null;
                              },
                            );
                          }),
                    ),
                  ],
                ),
                const Gap(14),
                Row(
                  children: [
                    Expanded(
                      child: ValueListenableBuilder<String?>(
                          valueListenable: selectedState,
                          builder: (context, state, _) {
                            return AppDropDown(
                              title: 'State',
                              onSelect: (value) {
                                selectedState.value = value;
                              },
                              selectedValue: state,
                              items: stateList
                                  .map((e) => DropdownMenuItem<String>(
                                        value: e,
                                        child: Text(
                                          e,
                                        ),
                                      ))
                                  .toList(),
                              hintText: 'Select',
                              hintTextColor: AppColors.text,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select state';
                                }
                                return null;
                              },
                            );
                          }),
                    ),
                    const Gap(15),
                    Expanded(
                      child: ValueListenableBuilder<String?>(
                          valueListenable: selectedCountry,
                          builder: (context, country, _) {
                            return AppDropDown(
                              title: 'Country',
                              onSelect: (value) {
                                selectedCity.value = value;
                              },
                              selectedValue: country,
                              items: countryList
                                  .map((e) => DropdownMenuItem<String>(
                                        value: e,
                                        child: Text(
                                          e,
                                        ),
                                      ))
                                  .toList(),
                              hintText: 'Select',
                              hintTextColor: AppColors.text,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select country';
                                }
                                return null;
                              },
                            );
                          }),
                    ),
                  ],
                ),
                const Gap(30),
                ValueListenableBuilder<bool>(
                    valueListenable: termsAccepted,
                    builder: (context, terms, _) {
                      return InkWell(
                        onTap: () {
                          termsAccepted.value = !termsAccepted.value;
                        },
                        child: Row(
                          children: [
                            AppSvgImage(terms ? AppAssets.selectedCheckboxIcon : AppAssets.unselectedCheckboxIcon),
                            const Gap(8),
                            Text(
                              'Terms and Conditions',
                              style: Theme.of(context).textTheme.bodyLarge,
                            )
                          ],
                        ),
                      );
                    }),
                const Gap(30),
                CommonButton(
                  onTap: () {
                    // validateForm();
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const SetUpBusinessPage()));
                  },
                  text: 'Signup',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void validateForm() {
    if (pickedImageFile.value == null) {
      Utility.toast(message: 'Please select profile image');
    } else if (formKey.currentState!.validate()) {
      if (termsAccepted.value) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VerificationPage(
              email: emailController.text.trim(),
            ),
          ),
        );
      } else {
        Utility.toast(message: 'Please accept terms and conditions');
      }
    }
  }
}
