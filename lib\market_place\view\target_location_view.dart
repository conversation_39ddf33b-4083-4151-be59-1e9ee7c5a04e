import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';

class TargetLocationView extends StatefulWidget {
  const TargetLocationView({super.key, required this.postAdModel, required this.onModelUpdated});
  final PostOrEventModel? postAdModel;
  final Function(PostOrEventModel) onModelUpdated;

  @override
  State<TargetLocationView> createState() => _TargetLocationViewState();
}

class _TargetLocationViewState extends State<TargetLocationView> {
  final countryList = ValueNotifier<List<String>>([
    'India',
    'USA',
    'UK',
  ]);
  final stateList = ValueNotifier<List<String>>([
    'Maharashtra',
    'Gujarat',
    'Karnataka',
  ]);
  final cityList = ValueNotifier<List<String>>([
    'Mumbai',
    'Pune',
    'Vadodara',
  ]);

  final selectedCountry = ValueNotifier<String?>(null);
  final selectedState = ValueNotifier<String?>(null);
  final selectedCity = ValueNotifier<String?>(null);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Target Location',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primary,
                ),
          ),
          const Gap(14),
          ValueListenableBuilder<List<String>>(
              valueListenable: countryList,
              builder: (context, country, _) {
                return ValueListenableBuilder<String?>(
                    valueListenable: selectedCountry,
                    builder: (context, selectValue, _) {
                      return AppDropDown(
                        onSelect: (value) {
                          selectedCountry.value = value;
                          widget.onModelUpdated(widget.postAdModel!.copyWith(country: value));
                        },
                        items: country.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                        selectedValue: selectValue,
                        hintText: 'Select',
                        title: 'Country',
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a country';
                          }
                          return null;
                        },
                      );
                    });
              }),
          const Gap(14),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ValueListenableBuilder<List<String>>(
                    valueListenable: stateList,
                    builder: (context, state, _) {
                      return ValueListenableBuilder<String?>(
                          valueListenable: selectedState,
                          builder: (context, selectValue, _) {
                            return AppDropDown(
                              onSelect: (value) {
                                selectedState.value = value;
                                widget.onModelUpdated(widget.postAdModel!.copyWith(state: value));
                              },
                              items: state.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                              selectedValue: selectValue,
                              hintText: 'Select',
                              title: 'State',
                              validator: (value) {
                                if (value == null) {
                                  return 'Please select a state';
                                }
                                return null;
                              },
                            );
                          });
                    }),
              ),
              const Gap(15),
              Expanded(
                child: ValueListenableBuilder<List<String>>(
                    valueListenable: cityList,
                    builder: (context, city, _) {
                      return ValueListenableBuilder<String?>(
                          valueListenable: selectedCity,
                          builder: (context, selectValue, _) {
                            return AppDropDown(
                              onSelect: (value) {
                                selectedCity.value = value;
                                widget.onModelUpdated(widget.postAdModel!.copyWith(city: value));
                              },
                              items: city.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                              selectedValue: selectValue,
                              hintText: 'Select',
                              title: 'City',
                              validator: (value) {
                                if (value == null) {
                                  return 'Please select a city';
                                }
                                return null;
                              },
                            );
                          });
                    }),
              ),
            ],
          )
        ],
      ),
    );
  }
}
