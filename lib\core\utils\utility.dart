import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:permission_handler/permission_handler.dart';

class Utility {
  // Flutter toast
  static void toast({required String? message, Color? color}) {
    if (message != null) {
      Fluttertoast.showToast(msg: message, backgroundColor: color ?? AppColors.white, textColor: AppColors.primary);
    }
  }

  // Email validation
  static bool isValidEmail(String email) {
    return RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(email);
  }

  // Request gallery permission
  Future<bool> requestGalleryPermission() async {
    final storagePermission = await Permission.storage.request();
    final photoPermission = await Permission.photos.request();

    return storagePermission.isGranted || photoPermission.isGranted;
  }

  // Pick multiple images
  static Future<List<PlatformFile>> pickMultiImageNew() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: true,
    );

    if (result != null) {
      return result.files;
    } else {
      return [];
    }
  }

  // Image loader
  static Widget imageLoader(
      {required String url,
      required String placeholder,
      double? height,
      double? width,
      BoxFit? fit,
      BuildContext? context,
      bool isShapeCircular = false,
      BorderRadius? borderRadius,
      List<BoxShadow>? boxShadow,
      BoxShape? shape}) {
    if (url.trim() == '') {
      return Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          shape: shape ?? BoxShape.rectangle,
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(0),
          boxShadow: boxShadow,
          image: DecorationImage(
            image: AssetImage(placeholder),
            fit: fit ?? BoxFit.cover,
          ),
        ),
      );
    }
    if (!url.startsWith('http')) url = AppStrings.storageUrl + url;
    return CachedNetworkImage(
      imageUrl: url,
      imageBuilder: (context, imageProvider) => Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          // borderRadius: borderRadius ?? BorderRadius.circular(10),
          shape: shape ?? BoxShape.rectangle,
          boxShadow: boxShadow,
          image: DecorationImage(
            image: imageProvider,
            fit: fit ?? BoxFit.cover,
          ),
        ),
      ),
      errorWidget: (context, error, dynamic a) => Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          shape: shape ?? BoxShape.rectangle,
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          // borderRadius: borderRadius ??  BorderRadius.circular(10),
          boxShadow: boxShadow,
          image: DecorationImage(
            image: AssetImage(placeholder),
            fit: fit ?? BoxFit.cover,
          ),
        ),
      ),
      placeholder: (context, url) => Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: boxShadow,
          color: AppColors.background,
        ),
      ),
    );
  }
}
