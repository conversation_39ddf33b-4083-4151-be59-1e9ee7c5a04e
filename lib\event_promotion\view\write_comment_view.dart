import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';

class WriteCommentView extends StatelessWidget {
  const WriteCommentView({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 538, // fixed height
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 55,
              height: 5,
              decoration: BoxDecoration(
                color: AppColors.border,
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            const Gap(20),
            Text(
              'Write comment',
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            const Gap(4),
            Text(
              'Please share your opinion about event/promotion',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(20),
            const AppTextFormField(
              title: 'Your comment',
              maxLines: 6,
            ),
            const Gap(30),
            CommonButton(
              onTap: () {
                Navigator.pop(context);
              },
              text: 'Post Comment',
            ),
            const Gap(30),
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
