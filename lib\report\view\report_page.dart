import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/report/widget/report_widget.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class ReportPage extends StatefulWidget {
  const ReportPage({super.key});

  @override
  State<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends State<ReportPage> {
  final listOfReport = ValueNotifier([
    'Abusing Content',
    'Suspicious',
    'It’s spam',
    'Bullying or harassment',
    'Sale of illegal or regulated goods',
    'Hate speech or symbols',
    'Nudity or sexual activity',
    'Scam or fraud',
    'Something else',
  ]);
  final selectedReport = ValueNotifier<String?>(null);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Report',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ValueListenableBuilder(
        valueListenable: listOfReport,
        builder: (context, list, _) {
          return ValueListenableBuilder(
            valueListenable: selectedReport,
            builder: (context, select, _) {
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 20,
                    horizontal: 20,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Why are you reporting?',
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                      const Gap(20),
                      ListView.separated(
                        separatorBuilder: (context, index) {
                          return const Gap(20);
                        },
                        itemCount: list.length,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          return ReportWidget(
                            title: list[index],
                            onTap: () {
                              selectedReport.value = list[index];
                            },
                            isSelected: select == list[index],
                          );
                        },
                      ),
                      if (select == 'Something else') ...[
                        const Gap(14),
                        Padding(
                          padding: const EdgeInsets.only(left: 30),
                          child: TextFormField(
                            maxLines: 4,
                            decoration: const InputDecoration(
                              hintText: 'Write detail here...',
                              fillColor: AppColors.white,
                              contentPadding: EdgeInsets.symmetric(
                                vertical: 14,
                                horizontal: 16,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(10),
                                ),
                                borderSide: BorderSide(
                                  color: AppColors.border,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(10),
                                ),
                                borderSide: BorderSide(
                                  color: AppColors.border,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                      const Gap(120),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
      bottomSheet: BottomButtonWidget(
        onTap: () {},
        text: 'Report',
      ),
    );
  }
}
