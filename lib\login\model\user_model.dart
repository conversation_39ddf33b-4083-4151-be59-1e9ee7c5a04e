import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  const UserModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.role,
    required this.gender,
    required this.year,
    required this.otpExpiresAt,
    required this.isVerified,
    required this.isPremium,
    required this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? fullName;
  final String? email;
  final String? role;
  final String? gender;
  final String? year;
  final DateTime? otpExpiresAt;
  final int? isVerified;
  final int? isPremium;
  final dynamic emailVerifiedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json["id"],
      fullName: json["full_name"],
      email: json["email"],
      role: json["role"],
      gender: json["gender"],
      year: json["year"],
      otpExpiresAt: DateTime.tryParse(json["otp_expires_at"] ?? ""),
      isVerified: json["is_verified"],
      isPremium: json["is_premium"],
      emailVerifiedAt: json["email_verified_at"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "full_name": fullName,
        "email": email,
        "role": role,
        "gender": gender,
        "year": year,
        "otp_expires_at": otpExpiresAt?.toIso8601String(),
        "is_verified": isVerified,
        "is_premium": isPremium,
        "email_verified_at": emailVerifiedAt,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  List<Object?> get props => [
        id,
        fullName,
        email,
        role,
        gender,
        year,
        otpExpiresAt,
        isVerified,
        isPremium,
        emailVerifiedAt,
        createdAt,
        updatedAt,
      ];

  UserModel copyWith({
    int? id,
    String? fullName,
    String? email,
    String? role,
    String? gender,
    String? year,
    DateTime? otpExpiresAt,
    int? isVerified,
    int? isPremium,
    dynamic emailVerifiedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? transactionId,
  }) {
    return UserModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      role: role ?? this.role,
      gender: gender ?? this.gender,
      year: year ?? this.year,
      otpExpiresAt: otpExpiresAt ?? this.otpExpiresAt,
      isVerified: isVerified ?? this.isVerified,
      isPremium: isPremium ?? this.isPremium,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
