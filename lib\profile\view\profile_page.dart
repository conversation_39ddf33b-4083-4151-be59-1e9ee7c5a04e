import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/profile/widget/profile_category_view.dart';
import 'package:leiuniverse/profile/widget/profile_detail_view.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Profile',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: IconButton(
              onPressed: () {},
              icon: const AppSvgImage(AppAssets.editIcon),
            ),
          )
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ProfileDetailView(),
            const Divider(
              height: 4,
              thickness: 4,
              color: AppColors.border,
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Business/Services'.toUpperCase(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.subText),
                      ),
                      CommonButton(
                        onTap: () {},
                        text: 'Add Business',
                        borderRadius: 8,
                        removeShadow: true,
                        width: 109,
                        fontSize: 14,
                        height: 30,
                        padding: const EdgeInsets.symmetric(vertical: 4),
                      )
                    ],
                  ),
                  const Gap(20),
                  ListView.separated(
                    itemCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return const ProfileCategoryView(
                        isEditable: true,
                      );
                    },
                    separatorBuilder: (context, index) => const Gap(20),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
