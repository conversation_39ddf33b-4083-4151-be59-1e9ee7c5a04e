import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/login/widget/login_option_widget.dart';
import 'package:leiuniverse/signup/view/business_register_view.dart';
import 'package:leiuniverse/signup/view/visitor_register_view.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final isBussinessLogin = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Signup',
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Register as',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                  ),
                  const Gap(14),
                  ValueListenableBuilder<bool>(
                      valueListenable: isBussinessLogin,
                      builder: (context, value, _) {
                        return Row(
                          children: [
                            LoginOptionWidget(
                              isSelected: !value,
                              title: 'Visitor ID',
                              onTap: () {
                                isBussinessLogin.value = false;
                              },
                            ),
                            const Gap(40),
                            LoginOptionWidget(
                              isSelected: value,
                              title: 'Bussiness ID',
                              onTap: () {
                                isBussinessLogin.value = true;
                              },
                            ),
                          ],
                        );
                      }),
                  const Gap(15),
                ],
              ),
            ),
            ValueListenableBuilder<bool>(
                valueListenable: isBussinessLogin,
                builder: (context, value, _) {
                  return value ? const BusinessRegisterView() : const VisitorRegisterView();
                }),
            Padding(
              padding: const EdgeInsets.all(22),
              child: Center(
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(text: 'Already have an account?', style: Theme.of(context).textTheme.bodyMedium),
                        TextSpan(
                          text: ' Login ',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.primary),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
