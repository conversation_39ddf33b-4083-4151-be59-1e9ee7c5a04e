import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';

class TermsPrivacyTabView extends StatelessWidget {
  const TermsPrivacyTabView(
      {super.key, this.onTapTerms, this.onTapPrivacy, this.isTermsSelected = false, this.isPrivacySelected = false});
  final void Function()? onTapTerms;
  final void Function()? onTapPrivacy;
  final bool isTermsSelected;
  final bool isPrivacySelected;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 234,
        height: 34,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: onTapTerms,
                child: Container(
                  decoration: BoxDecoration(
                    color: isTermsSelected ? AppColors.primary : AppColors.white,
                    borderRadius: BorderRadius.circular(100),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'T&C',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(height: 1.2, color: isTermsSelected ? AppColors.white : null),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: onTapPrivacy,
                child: Container(
                  decoration: BoxDecoration(
                    color: isPrivacySelected ? AppColors.primary : AppColors.white,
                    borderRadius: BorderRadius.circular(100),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Privacy',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(height: 1.2, color: isPrivacySelected ? AppColors.white : null),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
