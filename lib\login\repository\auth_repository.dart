part of 'i_auth_repository.dart';

@Injectable(as: IAuthRepository)
class AuthRepository extends IAuthRepository {
  AuthRepository(super.client, super.localStorageRepository);

  @override
  ApiResult<UserResponse> login({required String email, required String password, required String firebaseId}) async {
    final response = await client.post(
      url: AppStrings.login,
      requests: {
        'email': email.trim(),
        'password': password.trim(),
        'firebase_ids': firebaseId.trim(),
      },
    );

    return response.parseResponse(UserResponse.fromJson);
  }

  @override
  ApiResult<UserResponse> signUp(
      {required String fullName,
      required String email,
      required String password,
      required String confirmPassword,
      required String firebaseId}) async {
    final response = await client.post(
      url: AppStrings.signUp,
      requests: {
        'email': email.trim(),
        'full_name': fullName.trim(),
        'password': password.trim(),
        'password_confirmation': confirmPassword.trim(),
        'firebase_ids': firebaseId.trim(),
      },
    );

    return response.parseResponse(UserResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> forgotPassword({required String email}) async {
    final response = await client.post(
      url: AppStrings.forgotPassword,
      requests: {
        'email': email.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> verify({required String email, required String otp}) async {
    final response = await client.post(
      url: AppStrings.verifyOtp,
      requests: {
        'email': email.trim(),
        'otp': otp.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> resetPassword({required String email, required String password}) async {
    final response = await client.post(
      url: AppStrings.resetPassword,
      requests: {
        'email': email.trim(),
        'password': password.trim(),
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<UserResponse> update({String? fullName, String? gender, String? universityId, String? year}) async {
    final response = await client.post(
      url: AppStrings.updateProfile,
      requests: {
        if (fullName != null) 'full_name': fullName.trim(),
        if (gender != null) 'gender': gender.trim(),
        if (universityId != null) 'university_id': universityId.trim(),
        if (year != null) 'year': year.trim(),
      },
    );

    return response.parseResponse(UserResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> logout() async {
    final response = await client.post(
      url: AppStrings.logout,
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteAccount() async {
    final response = await client.delete(
      url: AppStrings.deleteAccount,
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<UserResponse> userDetail({required String userId}) async {
    final response = await client.get(
      url: AppStrings.userDetail(userId),
    );
    return response.parseResponse(UserResponse.fromJson);
  }
}
