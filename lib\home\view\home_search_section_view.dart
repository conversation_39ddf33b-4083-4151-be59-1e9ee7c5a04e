import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/more/view/more_page.dart';
import 'package:leiuniverse/notification/view/notification_page.dart';
import 'package:leiuniverse/profile/view/profile_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';

class HomeSearchSection extends StatefulWidget {
  const HomeSearchSection({super.key, this.onTap});
  final void Function()? onTap;

  @override
  State<HomeSearchSection> createState() => _HomeSearchSectionState();
}

class _HomeSearchSectionState extends State<HomeSearchSection> {
  final searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 55, 20, 20),
      decoration: const BoxDecoration(
        color: AppColors.whiteOffColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(23),
          bottomRight: Radius.circular(23),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              // Left: User avatar
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ProfilePage(),
                    ),
                  );
                },
                child: Utility.imageLoader(
                  url: '',
                  placeholder: AppAssets.userAvtarImage,
                  height: 32,
                  width: 32,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: AppColors.black.withOpacity2(0.15),
                    ),
                  ],
                ),
              ),

              // Center: Logo and App name
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Image.asset(
                      AppAssets.logo,
                      height: 26,
                      width: 26,
                    ),
                    const Gap(6),
                    Text(
                      AppStrings.appName,
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                            fontSize: 16,
                          ),
                    ),
                  ],
                ),
              ),

              // Right: Notification and menu icons
              Row(
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationPage(),
                        ),
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      child: AppSvgImage(AppAssets.notificationIcon),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MorePage(),
                        ),
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.only(left: 5),
                      child: AppSvgImage(AppAssets.menuDotsIcon),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const Gap(43),
          Row(
            children: [
              Text('Hello Jenny', style: Theme.of(context).textTheme.titleMedium),
              const Gap(4),
              const AppSvgImage(AppAssets.handIcon),
            ],
          ),
          const Gap(17),
          Text(AppStrings.homePageTagline, style: Theme.of(context).textTheme.headlineMedium),
          const Gap(34),
          AppTextFormField(
            hintText: 'Search by business, user...',
            fillColor: AppColors.white,
            controller: searchController,
            onTap: widget.onTap,
            readOnly: true,
            suffixIcon: const Padding(
              padding: EdgeInsets.all(10.0),
              child: AppSvgImage(AppAssets.homeSearchIcon),
            ),
          )
        ],
      ),
    );
  }
}
