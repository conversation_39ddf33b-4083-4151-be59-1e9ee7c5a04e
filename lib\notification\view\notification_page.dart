import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Notification',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ListView.builder(
        itemCount: 13,
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
        itemBuilder: (context, index) {
          return notificationView(
            title: '<PERSON> doe sent a message',
            content: 'You’ve got a new message! Tap to view and reply now hello.',
            time: 'Today 10:42 am',
            isNew: index == 0,
          );
        },
      ),
    );
  }

  Widget notificationView({String? title, String? content, String? time, bool isNew = false}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                title ?? '',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            if (isNew) ...[
              const Gap(10),
              const AppSvgImage(AppAssets.doteIcon),
            ],
          ],
        ),
        const Gap(2),
        Text(
          content ?? '',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const Gap(2),
        Text(
          time ?? '',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
        ),
        const Gap(16),
        const Divider(
          height: 1,
          color: AppColors.border,
        ),
        const Gap(16),
      ],
    );
  }
}
