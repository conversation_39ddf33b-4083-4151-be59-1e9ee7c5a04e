import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/market_place/view/market_place_details_page.dart';
import 'package:leiuniverse/market_place/view/my_ad_page.dart';
import 'package:leiuniverse/market_place/view/post_ad_page.dart';
import 'package:leiuniverse/market_place/widget/ad_post_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class MarketPlacePage extends StatefulWidget {
  const MarketPlacePage({super.key});

  @override
  State<MarketPlacePage> createState() => _MarketPlacePageState();
}

class _MarketPlacePageState extends State<MarketPlacePage> {
  final searchController = TextEditingController();
  final myAdsList = ValueNotifier<List<AdPostWidget>>([
    const AdPostWidget(),
    const AdPostWidget(),
    const AdPostWidget(),
  ]);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Marketplace',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: [
          CommonButton(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MyAdPage(),
                ),
              );
            },
            fontSize: 14,
            text: 'My Ads',
            removeShadow: true,
            width: 68,
            padding: const EdgeInsets.symmetric(
              vertical: 4,
            ),
            margin: const EdgeInsets.only(right: 24, top: 13, bottom: 13),
            borderRadius: 6,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            AppTextFormField(
              controller: searchController,
              hintText: 'Search',
              fillColor: AppColors.white,
              prefixIcon: const Padding(
                padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                child: AppSvgImage(
                  AppAssets.searchIcon,
                  color: AppColors.subText,
                ),
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: myAdsList,
                      builder: (context, addList, _) {
                        return ListView.separated(
                          separatorBuilder: (context, index) => const Gap(20),
                          itemCount: addList.length,
                          shrinkWrap: true,
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return AdPostWidget(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const MarketPlaceDetailsPage(),
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    ),
                    const Gap(20),
                    Align(
                      alignment: Alignment.topRight,
                      child: CommonButton(
                        width: 119,
                        borderRadius: 200,
                        fontSize: 15,
                        height: 40,
                        isLessShadow: true,
                        padding: EdgeInsets.zero,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const PostAdPage(),
                            ),
                          );
                        },
                        icon: const AppSvgImage(
                          AppAssets.postEventIcon,
                        ),
                        text: 'Post Ad',
                      ),
                    ),
                    const Gap(20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
