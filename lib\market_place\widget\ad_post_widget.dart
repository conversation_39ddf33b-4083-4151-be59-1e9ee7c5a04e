import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/user_info_widget.dart';

class AdPostWidget extends StatelessWidget {
  const AdPostWidget({super.key, this.onTap});
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add title here',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(4),
            Text(
              '\$150',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(14),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  4,
                  (index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: '',
                        placeholder: AppAssets.placeholderImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 93,
                        width: 93,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
              ),
            ),
            const Gap(14),
            const Divider(
              height: 0,
            ),
            const Gap(12),
            const UserInfoWidget(
              name: 'John Doe',
              businessName: 'Business name here',
              imageUrl: AppAssets.userAvtarImage,
            ),
          ],
        ),
      ),
    );
  }
}
